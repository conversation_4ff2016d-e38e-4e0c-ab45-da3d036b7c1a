package com.streamr.iptv.ui.home

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.streamr.iptv.databinding.FragmentHomeBinding
import com.streamr.iptv.ui.playlist.AddPlaylistActivity
import com.streamr.iptv.ui.adapters.MediaAdapter

class HomeFragment : Fragment() {
    
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: HomeViewModel by viewModels()
    
    private lateinit var continueWatchingAdapter: MediaAdapter
    private lateinit var recentMoviesAdapter: MediaAdapter
    private lateinit var trendingSeriesAdapter: MediaAdapter
    private lateinit var featuredChannelsAdapter: MediaAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerViews()
        setupClickListeners()
        observeViewModel()
    }
    
    private fun setupRecyclerViews() {
        // Continue Watching
        continueWatchingAdapter = MediaAdapter(
            onItemClick = { media ->
                // Handle continue watching item click
                viewModel.playMedia(media, requireContext())
            },
            onFavoriteClick = { media ->
                viewModel.toggleFavorite(media)
            }
        )
        binding.rvContinueWatching.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = continueWatchingAdapter
        }
        
        // Recent Movies
        recentMoviesAdapter = MediaAdapter(
            onItemClick = { media ->
                // Handle movie item click
                viewModel.playMedia(media, requireContext())
            },
            onFavoriteClick = { media ->
                viewModel.toggleFavorite(media)
            }
        )
        binding.rvRecentMovies.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = recentMoviesAdapter
        }

        // Trending Series
        trendingSeriesAdapter = MediaAdapter(
            onItemClick = { media ->
                // Handle series item click
                viewModel.playMedia(media, requireContext())
            },
            onFavoriteClick = { media ->
                viewModel.toggleFavorite(media)
            }
        )
        binding.rvTrendingSeries.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = trendingSeriesAdapter
        }

        // Featured Channels
        featuredChannelsAdapter = MediaAdapter(
            onItemClick = { media ->
                // Handle channel item click
                viewModel.playMedia(media, requireContext())
            },
            onFavoriteClick = { media ->
                viewModel.toggleFavorite(media)
            }
        )
        binding.rvFeaturedChannels.apply {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            adapter = featuredChannelsAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.btnAddPlaylist.setOnClickListener {
            startActivity(Intent(requireContext(), AddPlaylistActivity::class.java))
        }
    }
    
    private fun observeViewModel() {
        viewModel.continueWatching.observe(viewLifecycleOwner) { items ->
            if (items.isNotEmpty()) {
                binding.continueWatchingSection.visibility = View.VISIBLE
                continueWatchingAdapter.submitList(items)
            } else {
                binding.continueWatchingSection.visibility = View.GONE
            }
            updateEmptyState()
        }
        
        viewModel.recentMovies.observe(viewLifecycleOwner) { items ->
            if (items.isNotEmpty()) {
                binding.recentMoviesSection.visibility = View.VISIBLE
                recentMoviesAdapter.submitList(items)
            } else {
                binding.recentMoviesSection.visibility = View.GONE
            }
            updateEmptyState()
        }
        
        viewModel.trendingSeries.observe(viewLifecycleOwner) { items ->
            if (items.isNotEmpty()) {
                binding.trendingSeriesSection.visibility = View.VISIBLE
                trendingSeriesAdapter.submitList(items)
            } else {
                binding.trendingSeriesSection.visibility = View.GONE
            }
            updateEmptyState()
        }
        
        viewModel.featuredChannels.observe(viewLifecycleOwner) { items ->
            if (items.isNotEmpty()) {
                binding.featuredChannelsSection.visibility = View.VISIBLE
                featuredChannelsAdapter.submitList(items)
            } else {
                binding.featuredChannelsSection.visibility = View.GONE
            }
            updateEmptyState()
        }
        
        viewModel.hasPlaylists.observe(viewLifecycleOwner) { hasPlaylists ->
            updateEmptyState()
        }
    }
    
    private fun updateEmptyState() {
        val hasContent = binding.continueWatchingSection.visibility == View.VISIBLE ||
                binding.recentMoviesSection.visibility == View.VISIBLE ||
                binding.trendingSeriesSection.visibility == View.VISIBLE ||
                binding.featuredChannelsSection.visibility == View.VISIBLE
        
        binding.emptyState.visibility = if (hasContent) View.GONE else View.VISIBLE
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
