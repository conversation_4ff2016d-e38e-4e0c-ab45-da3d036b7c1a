package com.streamr.iptv.ui.home

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.*
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class HomeViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val continueWatching: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val recentChannels = repository.getRecentlyWatchedChannels()
        val recentMovies = repository.getRecentlyWatchedMovies()
        val recentSeries = repository.getRecentlyWatchedSeries()
        
        addSource(recentChannels) { channels ->
            val combined = mutableListOf<MediaItem>()
            channels?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            recentMovies.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            recentSeries.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
        
        addSource(recentMovies) { movies ->
            val combined = mutableListOf<MediaItem>()
            recentChannels.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            movies?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            recentSeries.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
        
        addSource(recentSeries) { series ->
            val combined = mutableListOf<MediaItem>()
            recentChannels.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            recentMovies.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            series?.let { combined.addAll(it.map { s -> MediaItem.fromSeries(s) }) }
            value = combined.sortedByDescending { it.lastWatched }.take(10)
        }
    }
    
    val recentMovies: LiveData<List<MediaItem>> = repository.getRecentlyAddedMovies().map { movies ->
        movies.map { MediaItem.fromMovie(it) }
    }
    
    val trendingSeries: LiveData<List<MediaItem>> = repository.getRecentlyWatchedSeries().map { series ->
        series.map { MediaItem.fromSeries(it) }
    }
    
    val featuredChannels: LiveData<List<MediaItem>> = repository.getFavoriteChannels().map { channels ->
        channels.map { MediaItem.fromChannel(it) }
    }
    
    val hasPlaylists: LiveData<Boolean> = repository.getActivePlaylists().map { playlists ->
        playlists.isNotEmpty()
    }
    
    fun playMedia(mediaItem: MediaItem, context: Context) {
        viewModelScope.launch {
            try {
                when (mediaItem.type) {
                    MediaItem.Type.CHANNEL -> {
                        // Get full channel data and play
                        val channel = repository.getChannelById(mediaItem.id)
                        channel?.let {
                            PlayerActivity.startWithChannel(context, it)
                        }
                    }
                    MediaItem.Type.MOVIE -> {
                        // Get full movie data and play
                        val movie = repository.getMovieById(mediaItem.id)
                        movie?.let {
                            PlayerActivity.startWithMovie(context, it, mediaItem.progress)
                        }
                    }
                    MediaItem.Type.SERIES -> {
                        // For series, we might want to show episodes list first
                        // For now, let's get the first unwatched episode
                        val episodes = repository.getEpisodesBySeries(mediaItem.id).value
                        val nextEpisode = episodes?.firstOrNull { !it.isWatched } ?: episodes?.firstOrNull()
                        nextEpisode?.let {
                            PlayerActivity.startWithEpisode(context, it, it.watchProgress)
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun toggleFavorite(mediaItem: MediaItem) {
        viewModelScope.launch {
            try {
                when (mediaItem.type) {
                    MediaItem.Type.CHANNEL -> {
                        repository.updateChannelFavoriteStatus(mediaItem.id, !mediaItem.isFavorite)
                    }
                    MediaItem.Type.MOVIE -> {
                        repository.updateMovieFavoriteStatus(mediaItem.id, !mediaItem.isFavorite)
                    }
                    MediaItem.Type.SERIES -> {
                        repository.updateSeriesFavoriteStatus(mediaItem.id, !mediaItem.isFavorite)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
