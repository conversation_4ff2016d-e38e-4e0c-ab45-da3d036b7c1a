<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    android:orientation="vertical">

    <!-- Header with date navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_dark"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Previous Day Button -->
        <ImageButton
            android:id="@+id/btnPreviousDay"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/text_primary" />

        <!-- Current Date -->
        <TextView
            android:id="@+id/tvCurrentDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Today, Dec 15"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- Next Day Button -->
        <ImageButton
            android:id="@+id/btnNextDay"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_forward"
            app:tint="@color/text_primary" />

    </LinearLayout>

    <!-- Time slots header -->
    <HorizontalScrollView
        android:id="@+id/timeScrollView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_medium"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/timeSlotContainer"
            android:layout_width="wrap_content"
            android:layout_height="48dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="80dp"
            android:paddingEnd="16dp" />

    </HorizontalScrollView>

    <!-- EPG Grid -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Channel list (fixed left column) -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvChannels"
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:background="@color/primary_medium"
            android:elevation="2dp" />

        <!-- Programs grid (scrollable) -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvEPGGrid"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="80dp"
            android:clipToPadding="false"
            android:scrollbars="none" />

        <!-- Current time indicator -->
        <View
            android:id="@+id/currentTimeIndicator"
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:layout_marginStart="80dp"
            android:background="@color/accent_green"
            android:visibility="visible" />

        <!-- Loading indicator -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminateTint="@color/accent_green"
            android:visibility="gone" />

        <!-- Empty state -->
        <LinearLayout
            android:id="@+id/emptyState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_tv"
                app:tint="@color/text_secondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No EPG data available"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Program guide will be updated automatically"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <!-- Bottom controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_dark"
        android:elevation="4dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Now button -->
        <Button
            android:id="@+id/btnNow"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="Now"
            android:textColor="@color/accent_green" />

        <!-- Prime time button -->
        <Button
            android:id="@+id/btnPrimeTime"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="Prime Time"
            android:textColor="@color/text_primary" />

        <!-- Search button -->
        <Button
            android:id="@+id/btnEPGSearch"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Search"
            android:textColor="@color/text_primary" />

    </LinearLayout>

</LinearLayout>
