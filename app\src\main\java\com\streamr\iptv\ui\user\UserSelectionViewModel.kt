package com.streamr.iptv.ui.user

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.UserProfile
import com.streamr.iptv.user.UserManager
import kotlinx.coroutines.launch

class UserSelectionViewModel(application: Application) : AndroidViewModel(application) {
    
    private val userManager = (application as StreamrApplication).userManager
    
    val userProfiles = userManager.allProfiles
    
    private val _profileSelectionResult = MutableLiveData<Result<UserProfile>>()
    val profileSelectionResult: LiveData<Result<UserProfile>> = _profileSelectionResult
    
    private val _profileCreationResult = MutableLiveData<Result<UserProfile>>()
    val profileCreationResult: LiveData<Result<UserProfile>> = _profileCreationResult
    
    /**
     * Select a user profile
     */
    fun selectProfile(profileId: Long, pin: String? = null) {
        viewModelScope.launch {
            try {
                val result = userManager.switchToProfile(profileId, pin)
                _profileSelectionResult.value = result
            } catch (e: Exception) {
                _profileSelectionResult.value = Result.failure(e)
            }
        }
    }
    
    /**
     * Create a new user profile
     */
    fun createProfile(
        name: String,
        email: String? = null,
        isKidsProfile: Boolean = false,
        parentalControlPin: String? = null
    ) {
        viewModelScope.launch {
            try {
                val result = userManager.createProfile(name, email, isKidsProfile, parentalControlPin)
                _profileCreationResult.value = result
            } catch (e: Exception) {
                _profileCreationResult.value = Result.failure(e)
            }
        }
    }
    
    /**
     * Create a default profile if none exists
     */
    fun createDefaultProfile() {
        createProfile("Default User")
    }
    
    /**
     * Delete a user profile
     */
    fun deleteProfile(profileId: Long) {
        viewModelScope.launch {
            try {
                userManager.deleteProfile(profileId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
