package com.streamr.iptv.download

import com.streamr.iptv.data.model.Download
import com.streamr.iptv.data.model.DownloadStatus
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.*
import okhttp3.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

/**
 * Individual download task that handles the actual file download
 */
class DownloadTask(
    private val download: Download,
    private val repository: StreamrRepository,
    private val httpClient: OkHttpClient,
    private val downloadDirectory: File
) {
    
    private var job: Job? = null
    private var isPaused = false
    private var isCancelled = false
    
    /**
     * Start the download
     */
    suspend fun start() {
        job = CoroutineScope(Dispatchers.IO).launch {
            try {
                executeDownload()
            } catch (e: CancellationException) {
                // Download was cancelled
                repository.updateDownloadStatus(download.id, DownloadStatus.CANCELLED)
            } catch (e: Exception) {
                // Download failed
                repository.updateDownloadStatus(download.id, DownloadStatus.FAILED)
                repository.updateDownloadError(download.id, e.message ?: "Unknown error")
            }
        }
        
        job?.join()
    }
    
    /**
     * Pause the download
     */
    fun pause() {
        isPaused = true
        job?.cancel()
    }
    
    /**
     * Cancel the download
     */
    fun cancel() {
        isCancelled = true
        job?.cancel()
    }
    
    /**
     * Execute the actual download
     */
    private suspend fun executeDownload() {
        if (isCancelled) return
        
        // Update status to downloading
        repository.updateDownloadStatus(download.id, DownloadStatus.DOWNLOADING)
        repository.updateDownloadStartTime(download.id, System.currentTimeMillis())
        
        val outputFile = File(downloadDirectory, download.fileName)
        val tempFile = File(downloadDirectory, "${download.fileName}.tmp")
        
        try {
            // Check if partial download exists
            val resumePosition = if (tempFile.exists()) tempFile.length() else 0L
            
            // Create HTTP request with range header for resume support
            val requestBuilder = Request.Builder()
                .url(download.streamUrl)
                .addHeader("User-Agent", "StreamrIPTV/1.0")
            
            if (resumePosition > 0) {
                requestBuilder.addHeader("Range", "bytes=$resumePosition-")
            }
            
            val request = requestBuilder.build()
            val response = httpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                throw IOException("HTTP error: ${response.code}")
            }
            
            val responseBody = response.body ?: throw IOException("Empty response body")
            val contentLength = responseBody.contentLength()
            val totalSize = if (contentLength > 0) contentLength + resumePosition else -1L
            
            // Update total file size if we got it from headers
            if (totalSize > 0 && download.fileSize == 0L) {
                repository.updateDownloadFileSize(download.id, totalSize)
            }
            
            downloadFile(responseBody.byteStream(), tempFile, resumePosition, totalSize)
            
            // Move temp file to final location
            if (tempFile.renameTo(outputFile)) {
                repository.updateDownloadFilePath(download.id, outputFile.absolutePath)
                repository.updateDownloadStatus(download.id, DownloadStatus.COMPLETED)
                repository.updateDownloadCompletedTime(download.id, System.currentTimeMillis())
                
                // Delete temp file if it still exists
                if (tempFile.exists()) {
                    tempFile.delete()
                }
            } else {
                throw IOException("Failed to move downloaded file")
            }
            
        } catch (e: Exception) {
            if (isCancelled) {
                // Clean up temp file on cancellation
                if (tempFile.exists()) {
                    tempFile.delete()
                }
            } else if (isPaused) {
                // Keep temp file for resume
                repository.updateDownloadStatus(download.id, DownloadStatus.PAUSED)
                repository.updateDownloadPausedTime(download.id, System.currentTimeMillis())
            } else {
                // Clean up on error
                if (tempFile.exists()) {
                    tempFile.delete()
                }
                throw e
            }
        }
    }
    
    /**
     * Download file with progress tracking
     */
    private suspend fun downloadFile(
        inputStream: InputStream,
        outputFile: File,
        resumePosition: Long,
        totalSize: Long
    ) {
        val buffer = ByteArray(8192) // 8KB buffer
        var downloadedBytes = resumePosition
        var lastUpdateTime = System.currentTimeMillis()
        var lastDownloadedBytes = resumePosition
        
        FileOutputStream(outputFile, resumePosition > 0).use { outputStream ->
            inputStream.use { input ->
                var bytesRead: Int
                
                while (input.read(buffer).also { bytesRead = it } != -1) {
                    if (isCancelled || isPaused) break
                    
                    outputStream.write(buffer, 0, bytesRead)
                    downloadedBytes += bytesRead
                    
                    // Update progress every second
                    val currentTime = System.currentTimeMillis()
                    if (currentTime - lastUpdateTime >= 1000) {
                        val progress = if (totalSize > 0) {
                            ((downloadedBytes.toDouble() / totalSize.toDouble()) * 100).toInt()
                        } else {
                            0
                        }
                        
                        val speed = if (currentTime > lastUpdateTime) {
                            ((downloadedBytes - lastDownloadedBytes) * 1000) / (currentTime - lastUpdateTime)
                        } else {
                            0L
                        }
                        
                        // Update database
                        repository.updateDownloadProgress(
                            download.id,
                            downloadedBytes,
                            progress,
                            speed
                        )
                        
                        lastUpdateTime = currentTime
                        lastDownloadedBytes = downloadedBytes
                        
                        // Yield to allow other coroutines to run
                        yield()
                    }
                }
            }
        }
        
        // Final progress update
        if (!isCancelled && !isPaused) {
            val finalProgress = if (totalSize > 0 && downloadedBytes >= totalSize) 100 else 99
            repository.updateDownloadProgress(download.id, downloadedBytes, finalProgress, 0L)
        }
    }
}

/**
 * Extension functions for repository to handle download updates
 */
suspend fun StreamrRepository.updateDownloadStatus(downloadId: Long, status: DownloadStatus) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadError(downloadId: Long, error: String) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadStartTime(downloadId: Long, startTime: Long) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadCompletedTime(downloadId: Long, completedTime: Long) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadPausedTime(downloadId: Long, pausedTime: Long) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadFileSize(downloadId: Long, fileSize: Long) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadFilePath(downloadId: Long, filePath: String) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.updateDownloadProgress(
    downloadId: Long,
    downloadedSize: Long,
    progress: Int,
    speed: Long
) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.getDownloadById(downloadId: Long): Download? {
    // TODO: Implement in repository
    return null
}

suspend fun StreamrRepository.insertDownload(download: Download): Long {
    // TODO: Implement in repository
    return 0L
}

suspend fun StreamrRepository.addToDownloadQueue(downloadId: Long) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.deleteDownload(downloadId: Long) {
    // TODO: Implement in repository
}

fun StreamrRepository.getAllDownloads(): androidx.lifecycle.LiveData<List<Download>> {
    // TODO: Implement in repository
    return androidx.lifecycle.MutableLiveData(emptyList())
}

fun StreamrRepository.getDownloadsByStatus(status: DownloadStatus): androidx.lifecycle.LiveData<List<Download>> {
    // TODO: Implement in repository
    return androidx.lifecycle.MutableLiveData(emptyList())
}

suspend fun StreamrRepository.getQueuedDownloads(): List<Download> {
    // TODO: Implement in repository
    return emptyList()
}

// Recording related extensions
suspend fun StreamrRepository.insertRecording(recording: com.streamr.iptv.data.model.Recording): Long {
    // TODO: Implement in repository
    return 0L
}

suspend fun StreamrRepository.updateRecordingStatus(recordingId: Long, status: com.streamr.iptv.data.model.RecordingStatus) {
    // TODO: Implement in repository
}

suspend fun StreamrRepository.getRecordingById(recordingId: Long): com.streamr.iptv.data.model.Recording? {
    // TODO: Implement in repository
    return null
}

fun StreamrRepository.getAllRecordings(): androidx.lifecycle.LiveData<List<com.streamr.iptv.data.model.Recording>> {
    // TODO: Implement in repository
    return androidx.lifecycle.MutableLiveData(emptyList())
}
