<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackgroundBorderless"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Profile Avatar -->
    <FrameLayout
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginBottom="8dp">

        <ImageView
            android:id="@+id/ivAvatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/profile_avatar_background"
            android:padding="16dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_person"
            app:tint="@color/text_primary" />

        <!-- Kids Profile Indicator -->
        <ImageView
            android:id="@+id/ivKidsIndicator"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="bottom|end"
            android:background="@drawable/kids_indicator_background"
            android:padding="4dp"
            android:src="@drawable/ic_child"
            android:visibility="gone"
            app:tint="@color/white"
            tools:visibility="visible" />

        <!-- Premium Indicator -->
        <ImageView
            android:id="@+id/ivPremiumIndicator"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top|end"
            android:background="@drawable/premium_indicator_background"
            android:padding="2dp"
            android:src="@drawable/ic_star"
            android:visibility="gone"
            app:tint="@color/white"
            tools:visibility="visible" />

    </FrameLayout>

    <!-- Profile Name -->
    <TextView
        android:id="@+id/tvProfileName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:maxLines="1"
        android:maxWidth="100dp"
        android:text="Profile Name"
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="John Doe" />

    <!-- Last Login -->
    <TextView
        android:id="@+id/tvLastLogin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="Last active"
        android:textColor="@color/text_secondary"
        android:textSize="12sp"
        android:visibility="gone"
        tools:text="2 hours ago"
        tools:visibility="visible" />

</LinearLayout>
