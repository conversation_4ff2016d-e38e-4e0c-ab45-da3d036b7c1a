package com.streamr.iptv.ui.downloads

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.streamr.iptv.databinding.FragmentDownloadListBinding
import com.streamr.iptv.ui.adapters.DownloadAdapter
import com.streamr.iptv.data.model.DownloadStatus

class CompletedDownloadsFragment : Fragment() {
    
    private var _binding: FragmentDownloadListBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: DownloadsViewModel by viewModels({ requireParentFragment() })
    private lateinit var adapter: DownloadAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDownloadListBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        adapter = DownloadAdapter(
            onPlayPauseClick = { download ->
                // Play completed download
                viewModel.playDownloadedFile(download)
            },
            onMoreClick = { download ->
                showDownloadOptions(download)
            },
            onItemClick = { download ->
                // Play completed download
                viewModel.playDownloadedFile(download)
            }
        )
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = <EMAIL>
        }
    }
    
    private fun observeViewModel() {
        viewModel.completedDownloads.observe(viewLifecycleOwner) { downloads ->
            adapter.submitList(downloads)
            
            // Show/hide empty state
            if (downloads.isEmpty()) {
                binding.recyclerView.visibility = View.GONE
                binding.emptyState.visibility = View.VISIBLE
                binding.tvEmptyMessage.text = "No completed downloads"
            } else {
                binding.recyclerView.visibility = View.VISIBLE
                binding.emptyState.visibility = View.GONE
            }
        }
    }
    
    private fun showDownloadOptions(download: com.streamr.iptv.data.model.Download) {
        // TODO: Show download options (Play, Delete, Share, etc.)
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
