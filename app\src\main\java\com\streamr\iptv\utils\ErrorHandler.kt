package com.streamr.iptv.utils

import android.content.Context
import android.util.Log
import com.google.android.exoplayer2.PlaybackException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Centralized error handling for Streamr IPTV
 */
object ErrorHandler {
    
    private const val TAG = "StreamrErrorHandler"
    
    /**
     * Handle ExoPlayer errors with user-friendly messages
     */
    fun handlePlaybackError(context: Context, error: PlaybackException): String {
        Log.e(TAG, "Playback error: ${error.message}", error)
        
        return when (error.errorCode) {
            PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED,
            PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT -> {
                "Network connection failed. Please check your internet connection."
            }
            PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS -> {
                "Stream not available. The content may have been removed or moved."
            }
            PlaybackException.ERROR_CODE_IO_FILE_NOT_FOUND -> {
                "Content not found. The stream URL may be incorrect."
            }
            PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED,
            PlaybackException.ERROR_CODE_PARSING_MANIFEST_MALFORMED -> {
                "Invalid stream format. This content cannot be played."
            }
            PlaybackException.ERROR_CODE_DECODER_INIT_FAILED -> {
                "Video decoder error. Your device may not support this video format."
            }
            PlaybackException.ERROR_CODE_AUDIO_TRACK_INIT_FAILED -> {
                "Audio playback error. Please check your audio settings."
            }
            PlaybackException.ERROR_CODE_DRM_SCHEME_UNSUPPORTED -> {
                "Protected content not supported. This content requires DRM support."
            }
            else -> {
                "Playback error occurred. Please try again or contact support."
            }
        }
    }
    
    /**
     * Handle network errors
     */
    fun handleNetworkError(context: Context, error: Throwable): String {
        Log.e(TAG, "Network error: ${error.message}", error)
        
        return when (error) {
            is UnknownHostException -> {
                "Cannot connect to server. Please check your internet connection."
            }
            is SocketTimeoutException -> {
                "Connection timeout. The server is taking too long to respond."
            }
            is IOException -> {
                "Network error occurred. Please check your connection and try again."
            }
            else -> {
                "Connection error. Please try again later."
            }
        }
    }
    
    /**
     * Handle API errors
     */
    fun handleApiError(context: Context, httpCode: Int, message: String?): String {
        Log.e(TAG, "API error: HTTP $httpCode - $message")
        
        return when (httpCode) {
            400 -> "Invalid request. Please check your playlist settings."
            401 -> "Authentication failed. Please check your username and password."
            403 -> "Access denied. Your account may not have permission to access this content."
            404 -> "Playlist not found. Please check the server URL."
            429 -> "Too many requests. Please wait a moment and try again."
            500, 502, 503, 504 -> "Server error. Please try again later."
            else -> "Connection error (HTTP $httpCode). Please try again."
        }
    }
    
    /**
     * Handle database errors
     */
    fun handleDatabaseError(context: Context, error: Throwable): String {
        Log.e(TAG, "Database error: ${error.message}", error)
        
        return when {
            error.message?.contains("UNIQUE constraint failed") == true -> {
                "Duplicate entry. This item already exists."
            }
            error.message?.contains("database is locked") == true -> {
                "Database is busy. Please try again in a moment."
            }
            error.message?.contains("no such table") == true -> {
                "Database error. Please restart the app."
            }
            else -> {
                "Data error occurred. Please restart the app if the problem persists."
            }
        }
    }
    
    /**
     * Handle playlist parsing errors
     */
    fun handlePlaylistError(context: Context, error: Throwable): String {
        Log.e(TAG, "Playlist error: ${error.message}", error)
        
        return when {
            error.message?.contains("M3U") == true -> {
                "Invalid M3U playlist format. Please check the playlist URL."
            }
            error.message?.contains("JSON") == true -> {
                "Invalid server response. Please check your Xtream Codes settings."
            }
            error.message?.contains("authentication") == true -> {
                "Authentication failed. Please check your credentials."
            }
            else -> {
                "Failed to load playlist. Please check your settings and try again."
            }
        }
    }
    
    /**
     * Log performance metrics
     */
    fun logPerformanceMetric(tag: String, operation: String, durationMs: Long) {
        if (durationMs > 5000) { // Log slow operations (>5 seconds)
            Log.w(TAG, "Slow operation: $tag.$operation took ${durationMs}ms")
        } else {
            Log.d(TAG, "Performance: $tag.$operation took ${durationMs}ms")
        }
    }
    
    /**
     * Log memory usage
     */
    fun logMemoryUsage(context: Context, tag: String) {
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val usedMemoryMB = usedMemory / (1024 * 1024)
        val maxMemoryMB = maxMemory / (1024 * 1024)
        
        Log.d(TAG, "Memory usage [$tag]: ${usedMemoryMB}MB / ${maxMemoryMB}MB")
        
        if (usedMemoryMB > maxMemoryMB * 0.8) {
            Log.w(TAG, "High memory usage detected: ${usedMemoryMB}MB / ${maxMemoryMB}MB")
        }
    }
    
    /**
     * Create error report for debugging
     */
    fun createErrorReport(
        context: Context,
        error: Throwable,
        userAction: String,
        additionalInfo: Map<String, String> = emptyMap()
    ): ErrorReport {
        return ErrorReport(
            timestamp = System.currentTimeMillis(),
            errorType = error::class.java.simpleName,
            errorMessage = error.message ?: "Unknown error",
            stackTrace = error.stackTraceToString(),
            userAction = userAction,
            deviceInfo = getDeviceInfo(context),
            additionalInfo = additionalInfo
        )
    }
    
    private fun getDeviceInfo(context: Context): Map<String, String> {
        return mapOf(
            "device_model" to android.os.Build.MODEL,
            "device_manufacturer" to android.os.Build.MANUFACTURER,
            "android_version" to android.os.Build.VERSION.RELEASE,
            "api_level" to android.os.Build.VERSION.SDK_INT.toString(),
            "app_version" to try {
                context.packageManager.getPackageInfo(context.packageName, 0).versionName
            } catch (e: Exception) {
                "unknown"
            }
        )
    }
}

/**
 * Error report data class
 */
data class ErrorReport(
    val timestamp: Long,
    val errorType: String,
    val errorMessage: String,
    val stackTrace: String,
    val userAction: String,
    val deviceInfo: Map<String, String>,
    val additionalInfo: Map<String, String>
) {
    fun toLogString(): String {
        return """
            |Error Report:
            |Timestamp: ${java.util.Date(timestamp)}
            |Type: $errorType
            |Message: $errorMessage
            |User Action: $userAction
            |Device: ${deviceInfo["device_manufacturer"]} ${deviceInfo["device_model"]}
            |Android: ${deviceInfo["android_version"]} (API ${deviceInfo["api_level"]})
            |App Version: ${deviceInfo["app_version"]}
            |Additional Info: $additionalInfo
            |Stack Trace:
            |$stackTrace
        """.trimMargin()
    }
}
