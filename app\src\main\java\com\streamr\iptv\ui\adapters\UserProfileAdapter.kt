package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.streamr.iptv.R
import com.streamr.iptv.data.model.UserProfile
import com.streamr.iptv.databinding.ItemUserProfileBinding
import java.text.SimpleDateFormat
import java.util.*

class UserProfileAdapter(
    private val onProfileClick: (UserProfile) -> Unit,
    private val onProfileLongClick: (UserProfile) -> Unit = {}
) : ListAdapter<UserProfile, UserProfileAdapter.UserProfileViewHolder>(UserProfileDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserProfileViewHolder {
        val binding = ItemUserProfileBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UserProfileViewHolder(binding, onProfileClick, onProfileLongClick)
    }

    override fun onBindViewHolder(holder: UserProfileViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class UserProfileViewHolder(
        private val binding: ItemUserProfileBinding,
        private val onProfileClick: (UserProfile) -> Unit,
        private val onProfileLongClick: (UserProfile) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(profile: UserProfile) {
            binding.apply {
                // Set profile name
                tvProfileName.text = profile.name

                // Set avatar
                if (!profile.avatarUrl.isNullOrEmpty()) {
                    Glide.with(ivAvatar.context)
                        .load(profile.avatarUrl)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .circleCrop()
                        .into(ivAvatar)
                } else {
                    ivAvatar.setImageResource(R.drawable.ic_person)
                }

                // Show kids indicator
                if (profile.isKidsProfile) {
                    ivKidsIndicator.visibility = View.VISIBLE
                } else {
                    ivKidsIndicator.visibility = View.GONE
                }

                // Show premium indicator
                if (profile.hasActiveSubscription()) {
                    ivPremiumIndicator.visibility = View.VISIBLE
                } else {
                    ivPremiumIndicator.visibility = View.GONE
                }

                // Show last login time
                if (profile.lastLoginAt != null && profile.lastLoginAt > 0) {
                    tvLastLogin.visibility = View.VISIBLE
                    tvLastLogin.text = formatLastLogin(profile.lastLoginAt)
                } else {
                    tvLastLogin.visibility = View.GONE
                }

                // Set click listeners
                root.setOnClickListener {
                    onProfileClick(profile)
                }

                root.setOnLongClickListener {
                    onProfileLongClick(profile)
                    true
                }
            }
        }

        private fun formatLastLogin(timestamp: Long): String {
            val now = System.currentTimeMillis()
            val diff = now - timestamp

            return when {
                diff < 60 * 1000 -> "Just now"
                diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)} min ago"
                diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)} hours ago"
                diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)} days ago"
                else -> {
                    val formatter = SimpleDateFormat("MMM dd", Locale.getDefault())
                    formatter.format(Date(timestamp))
                }
            }
        }
    }
}

class UserProfileDiffCallback : DiffUtil.ItemCallback<UserProfile>() {
    override fun areItemsTheSame(oldItem: UserProfile, newItem: UserProfile): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: UserProfile, newItem: UserProfile): Boolean {
        return oldItem == newItem
    }
}
