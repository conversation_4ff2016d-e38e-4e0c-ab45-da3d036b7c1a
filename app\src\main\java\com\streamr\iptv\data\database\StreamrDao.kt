package com.streamr.iptv.data.database

import androidx.lifecycle.LiveData
import androidx.room.*
import com.streamr.iptv.data.model.*

@Dao
interface PlaylistDao {
    @Query("SELECT * FROM playlists ORDER BY lastUsed DESC")
    fun getAllPlaylists(): LiveData<List<Playlist>>
    
    @Query("SELECT * FROM playlists WHERE isActive = 1 ORDER BY lastUsed DESC")
    fun getActivePlaylists(): LiveData<List<Playlist>>
    
    @Query("SELECT * FROM playlists WHERE id = :id")
    suspend fun getPlaylistById(id: Long): Playlist?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlaylist(playlist: Playlist): Long
    
    @Update
    suspend fun updatePlaylist(playlist: Playlist)
    
    @Delete
    suspend fun deletePlaylist(playlist: Playlist)
    
    @Query("UPDATE playlists SET lastUsed = :timestamp WHERE id = :id")
    suspend fun updateLastUsed(id: Long, timestamp: Long = System.currentTimeMillis())
}

@Dao
interface ChannelDao {
    @Query("SELECT * FROM channels WHERE playlistId = :playlistId ORDER BY name ASC")
    fun getChannelsByPlaylist(playlistId: Long): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE playlistId = :playlistId AND category = :category ORDER BY name ASC")
    fun getChannelsByCategory(playlistId: Long, category: ChannelCategory): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE isFavorite = 1 ORDER BY lastWatched DESC")
    fun getFavoriteChannels(): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE lastWatched IS NOT NULL ORDER BY lastWatched DESC LIMIT 10")
    fun getRecentlyWatchedChannels(): LiveData<List<Channel>>
    
    @Query("SELECT * FROM channels WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchChannels(query: String): LiveData<List<Channel>>

    @Query("SELECT * FROM channels WHERE id = :id")
    suspend fun getChannelById(id: Long): Channel?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertChannels(channels: List<Channel>)
    
    @Update
    suspend fun updateChannel(channel: Channel)
    
    @Query("UPDATE channels SET lastWatched = :timestamp, watchCount = watchCount + 1 WHERE id = :id")
    suspend fun updateWatchHistory(id: Long, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE channels SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean)
    
    @Query("DELETE FROM channels WHERE playlistId = :playlistId")
    suspend fun deleteChannelsByPlaylist(playlistId: Long)
}

@Dao
interface MovieDao {
    @Query("SELECT * FROM movies WHERE playlistId = :playlistId ORDER BY addedAt DESC")
    fun getMoviesByPlaylist(playlistId: Long): LiveData<List<Movie>>
    
    @Query("SELECT * FROM movies WHERE isFavorite = 1 ORDER BY lastWatched DESC")
    fun getFavoriteMovies(): LiveData<List<Movie>>
    
    @Query("SELECT * FROM movies WHERE lastWatched IS NOT NULL ORDER BY lastWatched DESC LIMIT 10")
    fun getRecentlyWatchedMovies(): LiveData<List<Movie>>
    
    @Query("SELECT * FROM movies WHERE addedAt > :timestamp ORDER BY addedAt DESC LIMIT 20")
    fun getRecentlyAddedMovies(timestamp: Long): LiveData<List<Movie>>
    
    @Query("SELECT * FROM movies WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchMovies(query: String): LiveData<List<Movie>>

    @Query("SELECT * FROM movies WHERE id = :id")
    suspend fun getMovieById(id: Long): Movie?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMovies(movies: List<Movie>)
    
    @Update
    suspend fun updateMovie(movie: Movie)
    
    @Query("UPDATE movies SET lastWatched = :timestamp, watchProgress = :progress WHERE id = :id")
    suspend fun updateWatchProgress(id: Long, progress: Long, timestamp: Long = System.currentTimeMillis())
    
    @Query("UPDATE movies SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean)

    @Query("UPDATE movies SET isWatched = 1, lastWatched = :timestamp WHERE id = :id")
    suspend fun markAsWatched(id: Long, timestamp: Long = System.currentTimeMillis())

    @Query("DELETE FROM movies WHERE playlistId = :playlistId")
    suspend fun deleteMoviesByPlaylist(playlistId: Long)
}

@Dao
interface SeriesDao {
    @Query("SELECT * FROM series WHERE playlistId = :playlistId ORDER BY addedAt DESC")
    fun getSeriesByPlaylist(playlistId: Long): LiveData<List<Series>>
    
    @Query("SELECT * FROM series WHERE isFavorite = 1 ORDER BY lastWatched DESC")
    fun getFavoriteSeries(): LiveData<List<Series>>
    
    @Query("SELECT * FROM series WHERE lastWatched IS NOT NULL ORDER BY lastWatched DESC LIMIT 10")
    fun getRecentlyWatchedSeries(): LiveData<List<Series>>
    
    @Query("SELECT * FROM series WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchSeries(query: String): LiveData<List<Series>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSeries(series: List<Series>)
    
    @Update
    suspend fun updateSeries(series: Series)
    
    @Query("UPDATE series SET isFavorite = :isFavorite WHERE id = :id")
    suspend fun updateFavoriteStatus(id: Long, isFavorite: Boolean)
    
    @Query("DELETE FROM series WHERE playlistId = :playlistId")
    suspend fun deleteSeriesByPlaylist(playlistId: Long)
}

@Dao
interface EpisodeDao {
    @Query("SELECT * FROM episodes WHERE seriesId = :seriesId ORDER BY seasonNumber ASC, episodeNumber ASC")
    fun getEpisodesBySeries(seriesId: Long): LiveData<List<Episode>>
    
    @Query("SELECT * FROM episodes WHERE seriesId = :seriesId AND seasonNumber = :season ORDER BY episodeNumber ASC")
    fun getEpisodesBySeason(seriesId: Long, season: Int): LiveData<List<Episode>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEpisodes(episodes: List<Episode>)
    
    @Update
    suspend fun updateEpisode(episode: Episode)
    
    @Query("UPDATE episodes SET lastWatched = :timestamp, watchProgress = :progress WHERE id = :id")
    suspend fun updateWatchProgress(id: Long, progress: Long, timestamp: Long = System.currentTimeMillis())

    @Query("UPDATE episodes SET isWatched = 1, lastWatched = :timestamp WHERE id = :id")
    suspend fun markAsWatched(id: Long, timestamp: Long = System.currentTimeMillis())

    @Query("DELETE FROM episodes WHERE seriesId = :seriesId")
    suspend fun deleteEpisodesBySeries(seriesId: Long)

    // ==================== EPG DAO Methods ====================

    // EPG Programs
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEPGPrograms(programs: List<EPGProgram>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEPGProgram(program: EPGProgram): Long

    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime >= :startTime AND endTime <= :endTime ORDER BY startTime ASC")
    fun getEPGProgramsForChannel(channelId: Long, startTime: Long, endTime: Long): LiveData<List<EPGProgram>>

    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime <= :currentTime AND endTime >= :currentTime LIMIT 1")
    suspend fun getCurrentProgram(channelId: Long, currentTime: Long): EPGProgram?

    @Query("SELECT * FROM epg_programs WHERE channelId = :channelId AND startTime > :currentTime ORDER BY startTime ASC LIMIT 1")
    suspend fun getNextProgram(channelId: Long, currentTime: Long): EPGProgram?

    @Query("SELECT * FROM epg_programs WHERE title LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%' ORDER BY startTime ASC")
    fun searchEPGPrograms(query: String): LiveData<List<EPGProgram>>

    @Query("SELECT * FROM epg_programs WHERE id = :programId")
    suspend fun getEPGProgramById(programId: Long): EPGProgram?

    @Query("DELETE FROM epg_programs WHERE channelId = :channelId")
    suspend fun deleteEPGProgramsByChannel(channelId: Long)

    @Query("DELETE FROM epg_programs WHERE endTime < :timestamp")
    suspend fun deleteOldEPGPrograms(timestamp: Long)

    // EPG Reminders
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEPGReminder(reminder: EPGReminder): Long

    @Query("SELECT * FROM epg_reminders WHERE isActive = 1 AND isTriggered = 0 AND reminderTime <= :currentTime")
    suspend fun getPendingReminders(currentTime: Long): List<EPGReminder>

    @Query("UPDATE epg_reminders SET isTriggered = 1 WHERE id = :reminderId")
    suspend fun markReminderAsTriggered(reminderId: Long)

    @Query("DELETE FROM epg_reminders WHERE programId = :programId")
    suspend fun deleteReminderByProgram(programId: Long)

    // EPG Recordings
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEPGRecording(recording: EPGRecording): Long

    @Query("SELECT * FROM epg_recordings WHERE status = :status")
    fun getRecordingsByStatus(status: RecordingStatus): LiveData<List<EPGRecording>>

    @Query("UPDATE epg_recordings SET status = :status WHERE id = :recordingId")
    suspend fun updateRecordingStatus(recordingId: Long, status: RecordingStatus)

    @Query("DELETE FROM epg_recordings WHERE id = :recordingId")
    suspend fun deleteRecording(recordingId: Long)

    // ==================== User Profile DAO Methods ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserProfile(profile: UserProfile): Long

    @Update
    suspend fun updateUserProfile(profile: UserProfile)

    @Query("DELETE FROM user_profiles WHERE id = :profileId")
    suspend fun deleteUserProfile(profileId: Long)

    @Query("SELECT * FROM user_profiles WHERE id = :profileId")
    suspend fun getUserProfileById(profileId: Long): UserProfile?

    @Query("SELECT * FROM user_profiles ORDER BY createdAt ASC")
    suspend fun getAllUserProfiles(): List<UserProfile>

    @Query("UPDATE user_profiles SET lastLoginAt = :timestamp WHERE id = :profileId")
    suspend fun updateUserLastLogin(profileId: Long, timestamp: Long)

    // ==================== User Settings DAO Methods ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserSettings(settings: UserSettings): Long

    @Update
    suspend fun updateUserSettings(settings: UserSettings)

    @Query("SELECT * FROM user_settings WHERE userId = :userId")
    suspend fun getUserSettings(userId: Long): UserSettings?

    // ==================== Watch History DAO Methods ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWatchHistory(history: WatchHistory): Long

    @Query("SELECT * FROM watch_history WHERE userId = :userId ORDER BY watchedAt DESC")
    fun getUserWatchHistory(userId: Long): LiveData<List<WatchHistory>>

    @Query("DELETE FROM watch_history WHERE id = :historyId")
    suspend fun deleteWatchHistory(historyId: Long)

    // ==================== User Favorites DAO Methods ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserFavorite(favorite: UserFavorite): Long

    @Query("DELETE FROM user_favorites WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun removeUserFavorite(userId: Long, mediaType: FavoriteMediaType, mediaId: Long)

    @Query("SELECT * FROM user_favorites WHERE userId = :userId ORDER BY addedAt DESC")
    fun getUserFavorites(userId: Long): LiveData<List<UserFavorite>>

    @Query("SELECT COUNT(*) > 0 FROM user_favorites WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun isInUserFavorites(userId: Long, mediaType: FavoriteMediaType, mediaId: Long): Boolean

    // ==================== User Watchlist DAO Methods ====================

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserWatchlist(watchlist: UserWatchlist): Long

    @Query("DELETE FROM user_watchlist WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun removeUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long)

    @Query("SELECT * FROM user_watchlist WHERE userId = :userId ORDER BY priority DESC, addedAt DESC")
    fun getUserWatchlist(userId: Long): LiveData<List<UserWatchlist>>

    @Query("SELECT COUNT(*) > 0 FROM user_watchlist WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun isInUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long): Boolean
}

// ==================== User Profile DAO ====================
@Dao
interface UserProfileDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserProfile(profile: UserProfile): Long

    @Update
    suspend fun updateUserProfile(profile: UserProfile)

    @Query("DELETE FROM user_profiles WHERE id = :profileId")
    suspend fun deleteUserProfile(profileId: Long)

    @Query("SELECT * FROM user_profiles WHERE id = :profileId")
    suspend fun getUserProfileById(profileId: Long): UserProfile?

    @Query("SELECT * FROM user_profiles ORDER BY createdAt ASC")
    suspend fun getAllUserProfiles(): List<UserProfile>

    @Query("UPDATE user_profiles SET lastLoginAt = :timestamp WHERE id = :profileId")
    suspend fun updateUserLastLogin(profileId: Long, timestamp: Long)
}

// ==================== User Settings DAO ====================
@Dao
interface UserSettingsDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserSettings(settings: UserSettings): Long

    @Update
    suspend fun updateUserSettings(settings: UserSettings)

    @Query("SELECT * FROM user_settings WHERE userId = :userId")
    suspend fun getUserSettings(userId: Long): UserSettings?
}

// ==================== Watch History DAO ====================
@Dao
interface WatchHistoryDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertWatchHistory(history: WatchHistory): Long

    @Query("SELECT * FROM watch_history WHERE userId = :userId ORDER BY watchedAt DESC")
    fun getUserWatchHistory(userId: Long): LiveData<List<WatchHistory>>

    @Query("DELETE FROM watch_history WHERE id = :historyId")
    suspend fun deleteWatchHistory(historyId: Long)
}

// ==================== User Favorites DAO ====================
@Dao
interface UserFavoriteDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserFavorite(favorite: UserFavorite): Long

    @Query("DELETE FROM user_favorites WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun removeUserFavorite(userId: Long, mediaType: FavoriteMediaType, mediaId: Long)

    @Query("SELECT * FROM user_favorites WHERE userId = :userId ORDER BY addedAt DESC")
    fun getUserFavorites(userId: Long): LiveData<List<UserFavorite>>

    @Query("SELECT COUNT(*) > 0 FROM user_favorites WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun isInUserFavorites(userId: Long, mediaType: FavoriteMediaType, mediaId: Long): Boolean
}

// ==================== User Watchlist DAO ====================
@Dao
interface UserWatchlistDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserWatchlist(watchlist: UserWatchlist): Long

    @Query("DELETE FROM user_watchlist WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun removeUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long)

    @Query("SELECT * FROM user_watchlist WHERE userId = :userId ORDER BY priority DESC, addedAt DESC")
    fun getUserWatchlist(userId: Long): LiveData<List<UserWatchlist>>

    @Query("SELECT COUNT(*) > 0 FROM user_watchlist WHERE userId = :userId AND mediaType = :mediaType AND mediaId = :mediaId")
    suspend fun isInUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long): Boolean
}
