package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.streamr.iptv.R
import com.streamr.iptv.data.model.Download
import com.streamr.iptv.data.model.DownloadStatus
import com.streamr.iptv.databinding.ItemDownloadBinding

class DownloadAdapter(
    private val onPlayPauseClick: (Download) -> Unit,
    private val onMoreClick: (Download) -> Unit,
    private val onItemClick: (Download) -> Unit
) : ListAdapter<Download, DownloadAdapter.DownloadViewHolder>(DownloadDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DownloadViewHolder {
        val binding = ItemDownloadBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DownloadViewHolder(binding, onPlayPauseClick, onMoreClick, onItemClick)
    }

    override fun onBindViewHolder(holder: DownloadViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class DownloadViewHolder(
        private val binding: ItemDownloadBinding,
        private val onPlayPauseClick: (Download) -> Unit,
        private val onMoreClick: (Download) -> Unit,
        private val onItemClick: (Download) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(download: Download) {
            binding.apply {
                // Set title
                tvTitle.text = download.title

                // Set quality
                tvQuality.text = download.quality.displayName

                // Set file size
                tvFileSize.text = download.getFormattedFileSize()

                // Set status
                tvStatus.text = getStatusText(download.status)
                tvStatus.setTextColor(getStatusColor(download.status))

                // Set progress
                progressBar.progress = download.progress
                tvProgress.text = "${download.progress}%"

                // Set speed (only show when downloading)
                if (download.status == DownloadStatus.DOWNLOADING && download.speed > 0) {
                    tvSpeed.text = download.getFormattedSpeed()
                    tvSpeed.visibility = View.VISIBLE
                } else {
                    tvSpeed.visibility = View.GONE
                }

                // Set time remaining
                if (download.status == DownloadStatus.DOWNLOADING) {
                    val timeRemaining = download.getEstimatedTimeRemaining()
                    if (timeRemaining > 0) {
                        tvTimeRemaining.text = formatTimeRemaining(timeRemaining)
                        tvTimeRemaining.visibility = View.VISIBLE
                    } else {
                        tvTimeRemaining.visibility = View.GONE
                    }
                } else {
                    tvTimeRemaining.visibility = View.GONE
                }

                // Set thumbnail
                if (!download.thumbnailUrl.isNullOrEmpty()) {
                    Glide.with(ivThumbnail.context)
                        .load(download.thumbnailUrl)
                        .placeholder(R.drawable.image_placeholder)
                        .error(R.drawable.image_placeholder)
                        .into(ivThumbnail)
                } else {
                    ivThumbnail.setImageResource(R.drawable.image_placeholder)
                }

                // Set play/pause button
                updatePlayPauseButton(download.status)

                // Show/hide progress bar based on status
                when (download.status) {
                    DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED, DownloadStatus.QUEUED -> {
                        progressBar.visibility = View.VISIBLE
                    }
                    else -> {
                        progressBar.visibility = View.GONE
                    }
                }

                // Set click listeners
                btnPlayPause.setOnClickListener {
                    onPlayPauseClick(download)
                }

                btnMore.setOnClickListener {
                    onMoreClick(download)
                }

                root.setOnClickListener {
                    onItemClick(download)
                }
            }
        }

        private fun updatePlayPauseButton(status: DownloadStatus) {
            binding.btnPlayPause.apply {
                when (status) {
                    DownloadStatus.DOWNLOADING -> {
                        setImageResource(R.drawable.ic_pause)
                        isEnabled = true
                        alpha = 1.0f
                    }
                    DownloadStatus.PAUSED, DownloadStatus.PENDING, DownloadStatus.QUEUED -> {
                        setImageResource(R.drawable.ic_play)
                        isEnabled = true
                        alpha = 1.0f
                    }
                    DownloadStatus.COMPLETED -> {
                        setImageResource(R.drawable.ic_play)
                        isEnabled = true
                        alpha = 1.0f
                    }
                    DownloadStatus.FAILED, DownloadStatus.CANCELLED -> {
                        setImageResource(R.drawable.ic_refresh)
                        isEnabled = true
                        alpha = 1.0f
                    }
                    else -> {
                        isEnabled = false
                        alpha = 0.5f
                    }
                }
            }
        }

        private fun getStatusText(status: DownloadStatus): String {
            return when (status) {
                DownloadStatus.PENDING -> "Pending"
                DownloadStatus.QUEUED -> "Queued"
                DownloadStatus.DOWNLOADING -> "Downloading"
                DownloadStatus.PAUSED -> "Paused"
                DownloadStatus.COMPLETED -> "Completed"
                DownloadStatus.FAILED -> "Failed"
                DownloadStatus.CANCELLED -> "Cancelled"
                DownloadStatus.EXPIRED -> "Expired"
            }
        }

        private fun getStatusColor(status: DownloadStatus): Int {
            val context = binding.root.context
            return when (status) {
                DownloadStatus.DOWNLOADING -> context.getColor(R.color.accent_green)
                DownloadStatus.COMPLETED -> context.getColor(R.color.accent_green)
                DownloadStatus.PAUSED -> context.getColor(R.color.text_secondary)
                DownloadStatus.FAILED, DownloadStatus.CANCELLED -> context.getColor(R.color.error_red)
                else -> context.getColor(R.color.text_secondary)
            }
        }

        private fun formatTimeRemaining(seconds: Long): String {
            return when {
                seconds < 60 -> "${seconds}s"
                seconds < 3600 -> "${seconds / 60}m ${seconds % 60}s"
                else -> "${seconds / 3600}h ${(seconds % 3600) / 60}m"
            }
        }
    }
}

class DownloadDiffCallback : DiffUtil.ItemCallback<Download>() {
    override fun areItemsTheSame(oldItem: Download, newItem: Download): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Download, newItem: Download): Boolean {
        return oldItem == newItem
    }
}
