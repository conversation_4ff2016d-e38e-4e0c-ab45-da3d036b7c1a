package com.streamr.iptv.ui.downloads

import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter

class DownloadsPagerAdapter(fragment: Fragment) : FragmentStateAdapter(fragment) {
    
    override fun getItemCount(): Int = 3
    
    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> ActiveDownloadsFragment()
            1 -> CompletedDownloadsFragment()
            2 -> RecordingsFragment()
            else -> ActiveDownloadsFragment()
        }
    }
}
