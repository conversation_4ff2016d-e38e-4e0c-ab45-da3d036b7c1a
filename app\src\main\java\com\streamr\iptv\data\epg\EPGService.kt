package com.streamr.iptv.data.epg

import com.streamr.iptv.data.model.*
import okhttp3.OkHttpClient
import okhttp3.Request
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Service for loading EPG (Electronic Program Guide) data
 */
class EPGService {
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .build()
    
    /**
     * Load EPG data from XMLTV URL
     */
    suspend fun loadEPGFromUrl(url: String): Result<List<EPGProgram>> {
        return try {
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "StreamrIPTV/1.0")
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val xmlContent = response.body?.string() ?: ""
                parseXMLTVContent(xmlContent)
            } else {
                Result.failure(Exception("HTTP Error: ${response.code} ${response.message}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Parse XMLTV content and extract EPG programs
     */
    private fun parseXMLTVContent(xmlContent: String): Result<List<EPGProgram>> {
        return try {
            val programs = mutableListOf<EPGProgram>()
            val channels = mutableMapOf<String, String>() // id to display-name mapping
            
            val factory = XmlPullParserFactory.newInstance()
            val parser = factory.newPullParser()
            parser.setInput(StringReader(xmlContent))
            
            var eventType = parser.eventType
            var currentProgram: EPGProgramBuilder? = null
            var currentChannelId: String? = null
            var currentTag: String? = null
            
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_TAG -> {
                        currentTag = parser.name
                        when (parser.name) {
                            "channel" -> {
                                val channelId = parser.getAttributeValue(null, "id")
                                currentChannelId = channelId
                            }
                            "display-name" -> {
                                // Will be handled in TEXT event
                            }
                            "programme" -> {
                                val channelId = parser.getAttributeValue(null, "channel")
                                val start = parser.getAttributeValue(null, "start")
                                val stop = parser.getAttributeValue(null, "stop")
                                
                                currentProgram = EPGProgramBuilder().apply {
                                    this.channelId = channelId
                                    this.startTime = parseXMLTVTime(start)
                                    this.endTime = parseXMLTVTime(stop)
                                }
                            }
                        }
                    }
                    
                    XmlPullParser.TEXT -> {
                        val text = parser.text?.trim()
                        if (!text.isNullOrEmpty()) {
                            when (currentTag) {
                                "display-name" -> {
                                    currentChannelId?.let { channelId ->
                                        channels[channelId] = text
                                    }
                                }
                                "title" -> {
                                    currentProgram?.title = text
                                }
                                "desc" -> {
                                    currentProgram?.description = text
                                }
                                "category" -> {
                                    currentProgram?.category = text
                                }
                                "episode-num" -> {
                                    // Parse episode information if available
                                    parseEpisodeInfo(text, currentProgram)
                                }
                                "date" -> {
                                    currentProgram?.year = text.take(4).toIntOrNull()
                                }
                                "rating" -> {
                                    currentProgram?.rating = text
                                }
                                "director" -> {
                                    currentProgram?.director = text
                                }
                                "actor" -> {
                                    val currentCast = currentProgram?.cast
                                    currentProgram?.cast = if (currentCast.isNullOrEmpty()) {
                                        text
                                    } else {
                                        "$currentCast, $text"
                                    }
                                }
                            }
                        }
                    }
                    
                    XmlPullParser.END_TAG -> {
                        when (parser.name) {
                            "programme" -> {
                                currentProgram?.let { builder ->
                                    // Find matching channel ID in our database
                                    val channelDbId = findChannelIdByTvgId(builder.channelId)
                                    if (channelDbId != null) {
                                        val program = builder.build(channelDbId)
                                        programs.add(program)
                                    }
                                }
                                currentProgram = null
                            }
                        }
                        currentTag = null
                    }
                }
                eventType = parser.next()
            }
            
            Result.success(programs)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Parse XMLTV time format (e.g., "20231201140000 +0000")
     */
    private fun parseXMLTVTime(timeString: String?): Long {
        if (timeString.isNullOrEmpty()) return 0
        
        return try {
            // Remove timezone info for simplicity
            val cleanTime = timeString.split(" ")[0]
            val format = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
            format.timeZone = TimeZone.getTimeZone("UTC")
            format.parse(cleanTime)?.time ?: 0
        } catch (e: Exception) {
            0
        }
    }
    
    /**
     * Parse episode information from XMLTV
     */
    private fun parseEpisodeInfo(episodeText: String, programBuilder: EPGProgramBuilder?) {
        programBuilder ?: return
        
        try {
            // Handle different episode number formats
            when {
                episodeText.contains(".") -> {
                    // Format: "season.episode"
                    val parts = episodeText.split(".")
                    if (parts.size >= 2) {
                        programBuilder.seasonNumber = parts[0].toIntOrNull()?.plus(1) // XMLTV uses 0-based
                        programBuilder.episodeNumber = parts[1].toIntOrNull()?.plus(1) // XMLTV uses 0-based
                    }
                }
                episodeText.contains("/") -> {
                    // Format: "episode/total"
                    val parts = episodeText.split("/")
                    if (parts.isNotEmpty()) {
                        programBuilder.episodeNumber = parts[0].toIntOrNull()
                    }
                }
                else -> {
                    // Simple episode number
                    programBuilder.episodeNumber = episodeText.toIntOrNull()
                }
            }
        } catch (e: Exception) {
            // Ignore parsing errors
        }
    }
    
    /**
     * Find channel database ID by TVG ID
     * This would need to be implemented based on your channel matching logic
     */
    private fun findChannelIdByTvgId(tvgId: String?): Long? {
        // TODO: Implement channel matching logic
        // This should query your database to find the channel with matching tvgId
        // For now, return null to skip unmatched channels
        return null
    }
    
    /**
     * Generate sample EPG data for testing
     */
    fun generateSampleEPGData(channelId: Long): List<EPGProgram> {
        val programs = mutableListOf<EPGProgram>()
        val calendar = Calendar.getInstance()
        
        // Generate programs for the next 24 hours
        repeat(24) { hour ->
            val startTime = calendar.timeInMillis
            calendar.add(Calendar.HOUR, 1)
            val endTime = calendar.timeInMillis
            
            val program = EPGProgram(
                channelId = channelId,
                title = "Sample Program ${hour + 1}",
                description = "This is a sample program description for testing EPG functionality.",
                category = when (hour % 4) {
                    0 -> "News"
                    1 -> "Entertainment"
                    2 -> "Sports"
                    else -> "Movies"
                },
                startTime = startTime,
                endTime = endTime,
                duration = 60,
                isLive = hour % 6 == 0,
                isPremiere = hour % 8 == 0,
                isHD = hour % 3 == 0
            )
            
            programs.add(program)
        }
        
        return programs
    }
}

/**
 * Builder class for EPG programs
 */
private class EPGProgramBuilder {
    var channelId: String? = null
    var title: String? = null
    var description: String? = null
    var category: String? = null
    var startTime: Long = 0
    var endTime: Long = 0
    var seasonNumber: Int? = null
    var episodeNumber: Int? = null
    var year: Int? = null
    var rating: String? = null
    var director: String? = null
    var cast: String? = null
    
    fun build(dbChannelId: Long): EPGProgram {
        val duration = if (endTime > startTime) {
            ((endTime - startTime) / (60 * 1000)).toInt()
        } else {
            60 // Default 1 hour
        }
        
        return EPGProgram(
            channelId = dbChannelId,
            title = title ?: "Unknown Program",
            description = description,
            category = category,
            startTime = startTime,
            endTime = endTime,
            duration = duration,
            seasonNumber = seasonNumber,
            episodeNumber = episodeNumber,
            year = year,
            rating = rating,
            director = director,
            cast = cast
        )
    }
}
