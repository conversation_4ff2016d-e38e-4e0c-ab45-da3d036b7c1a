package com.streamr.iptv.data.parser

import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.data.model.ChannelCategory
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.BufferedReader
import java.io.StringReader
import java.util.concurrent.TimeUnit

class M3UParser {
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .build()
    
    /**
     * Parse M3U playlist from URL
     */
    suspend fun parseFromUrl(url: String, playlistId: Long): Result<List<Channel>> {
        return try {
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "StreamrIPTV/1.0")
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            
            if (response.isSuccessful) {
                val content = response.body?.string() ?: ""
                parseFromString(content, playlistId)
            } else {
                Result.failure(Exception("HTTP Error: ${response.code} ${response.message}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Parse M3U playlist from string content
     */
    fun parseFromString(content: String, playlistId: Long): Result<List<Channel>> {
        return try {
            val channels = mutableListOf<Channel>()
            val reader = BufferedReader(StringReader(content))
            
            var line: String?
            var currentChannel: M3UChannelInfo? = null
            
            while (reader.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    when {
                        currentLine.startsWith("#EXTM3U") -> {
                            // M3U header - continue
                        }
                        currentLine.startsWith("#EXTINF:") -> {
                            // Channel info line
                            currentChannel = parseExtInfLine(currentLine)
                        }
                        currentLine.startsWith("http") || currentLine.startsWith("rtmp") || 
                        currentLine.startsWith("rtsp") || currentLine.startsWith("udp") -> {
                            // Stream URL
                            currentChannel?.let { channelInfo ->
                                val channel = Channel(
                                    playlistId = playlistId,
                                    name = channelInfo.name,
                                    streamUrl = currentLine.trim(),
                                    logoUrl = channelInfo.logoUrl,
                                    groupTitle = channelInfo.groupTitle,
                                    tvgId = channelInfo.tvgId,
                                    tvgName = channelInfo.tvgName,
                                    category = mapGroupToCategory(channelInfo.groupTitle),
                                    isHD = channelInfo.name.contains("HD", ignoreCase = true),
                                    language = channelInfo.language,
                                    country = channelInfo.country
                                )
                                channels.add(channel)
                                currentChannel = null
                            }
                        }
                        currentLine.startsWith("#") -> {
                            // Other M3U tags - ignore for now
                        }
                    }
                }
            }
            
            Result.success(channels)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Parse #EXTINF line to extract channel information
     */
    private fun parseExtInfLine(line: String): M3UChannelInfo {
        // Example: #EXTINF:-1 tvg-id="bbc1.uk" tvg-name="BBC One" tvg-logo="http://example.com/logo.png" group-title="UK",BBC One HD
        
        var name = ""
        var tvgId: String? = null
        var tvgName: String? = null
        var logoUrl: String? = null
        var groupTitle: String? = null
        var language: String? = null
        var country: String? = null
        
        try {
            // Extract the channel name (after the last comma)
            val lastCommaIndex = line.lastIndexOf(',')
            if (lastCommaIndex != -1 && lastCommaIndex < line.length - 1) {
                name = line.substring(lastCommaIndex + 1).trim()
            }
            
            // Extract attributes using regex
            val attributes = line.substring(0, if (lastCommaIndex != -1) lastCommaIndex else line.length)
            
            // Extract tvg-id
            tvgId = extractAttribute(attributes, "tvg-id")
            
            // Extract tvg-name
            tvgName = extractAttribute(attributes, "tvg-name")
            
            // Extract tvg-logo
            logoUrl = extractAttribute(attributes, "tvg-logo")
            
            // Extract group-title
            groupTitle = extractAttribute(attributes, "group-title")
            
            // Extract language
            language = extractAttribute(attributes, "tvg-language") 
                ?: extractAttribute(attributes, "language")
            
            // Extract country
            country = extractAttribute(attributes, "tvg-country") 
                ?: extractAttribute(attributes, "country")
            
            // If name is empty, try to use tvg-name
            if (name.isEmpty()) {
                name = tvgName ?: "Unknown Channel"
            }
            
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return M3UChannelInfo(
            name = name,
            tvgId = tvgId,
            tvgName = tvgName,
            logoUrl = logoUrl,
            groupTitle = groupTitle,
            language = language,
            country = country
        )
    }
    
    /**
     * Extract attribute value from M3U line
     */
    private fun extractAttribute(line: String, attributeName: String): String? {
        return try {
            val pattern = """$attributeName="([^"]*)"|\s$attributeName=([^\s,]+)""".toRegex()
            val matchResult = pattern.find(line)
            matchResult?.groupValues?.let { groups ->
                groups.getOrNull(1)?.takeIf { it.isNotEmpty() } 
                    ?: groups.getOrNull(2)?.takeIf { it.isNotEmpty() }
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Map group title to channel category
     */
    private fun mapGroupToCategory(groupTitle: String?): ChannelCategory {
        return when (groupTitle?.lowercase()) {
            "sports", "sport", "deportes" -> ChannelCategory.SPORTS
            "news", "noticias", "actualités" -> ChannelCategory.NEWS
            "entertainment", "entretenimiento", "divertissement" -> ChannelCategory.ENTERTAINMENT
            "kids", "children", "infantil", "enfants" -> ChannelCategory.KIDS
            "music", "música", "musique" -> ChannelCategory.MUSIC
            "documentary", "documentaries", "documental", "documentaire" -> ChannelCategory.DOCUMENTARY
            "lifestyle", "estilo de vida", "mode de vie" -> ChannelCategory.LIFESTYLE
            "movies", "películas", "films" -> ChannelCategory.MOVIES
            "series", "séries" -> ChannelCategory.SERIES
            else -> ChannelCategory.LIVE_TV
        }
    }
}

/**
 * Data class to hold M3U channel information
 */
data class M3UChannelInfo(
    val name: String,
    val tvgId: String? = null,
    val tvgName: String? = null,
    val logoUrl: String? = null,
    val groupTitle: String? = null,
    val language: String? = null,
    val country: String? = null
)
