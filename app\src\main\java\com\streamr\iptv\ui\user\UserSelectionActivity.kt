package com.streamr.iptv.ui.user

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.streamr.iptv.MainActivity
import com.streamr.iptv.databinding.ActivityUserSelectionBinding
import com.streamr.iptv.ui.adapters.UserProfileAdapter
import com.streamr.iptv.data.model.UserProfile

class UserSelectionActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityUserSelectionBinding
    private val viewModel: UserSelectionViewModel by viewModels()
    private lateinit var profileAdapter: UserProfileAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityUserSelectionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        observeViewModel()
    }
    
    private fun setupUI() {
        // Setup profiles RecyclerView
        profileAdapter = UserProfileAdapter(
            onProfileClick = { profile ->
                selectProfile(profile)
            },
            onProfileLongClick = { profile ->
                showProfileOptions(profile)
            }
        )
        
        binding.rvUserProfiles.apply {
            layoutManager = GridLayoutManager(this@UserSelectionActivity, 3)
            adapter = profileAdapter
        }
        
        // Setup buttons
        binding.btnAddProfile.setOnClickListener {
            showCreateProfileDialog()
        }
        
        binding.btnManageProfiles.setOnClickListener {
            startActivity(Intent(this, ProfileManagementActivity::class.java))
        }
    }
    
    private fun observeViewModel() {
        viewModel.userProfiles.observe(this) { profiles ->
            profileAdapter.submitList(profiles)
            
            // If no profiles exist, create a default one
            if (profiles.isEmpty()) {
                viewModel.createDefaultProfile()
            }
        }
        
        viewModel.profileSelectionResult.observe(this) { result ->
            result.fold(
                onSuccess = { profile ->
                    // Profile selected successfully, navigate to main activity
                    navigateToMainActivity()
                },
                onFailure = { error ->
                    showError(error.message ?: "Failed to select profile")
                }
            )
        }
        
        viewModel.profileCreationResult.observe(this) { result ->
            result.fold(
                onSuccess = { profile ->
                    showMessage("Profile created successfully")
                },
                onFailure = { error ->
                    showError(error.message ?: "Failed to create profile")
                }
            )
        }
    }
    
    private fun selectProfile(profile: UserProfile) {
        if (!profile.parentalControlPin.isNullOrEmpty()) {
            // Show PIN dialog for protected profiles
            showPinDialog(profile)
        } else {
            // Select profile directly
            viewModel.selectProfile(profile.id)
        }
    }
    
    private fun showProfileOptions(profile: UserProfile) {
        // TODO: Show profile options (Edit, Delete, etc.)
        showMessage("Profile options for: ${profile.name}")
    }
    
    private fun showCreateProfileDialog() {
        // TODO: Show create profile dialog
        CreateProfileDialog.show(this) { name, isKidsProfile ->
            viewModel.createProfile(name, isKidsProfile = isKidsProfile)
        }
    }
    
    private fun showPinDialog(profile: UserProfile) {
        // TODO: Show PIN input dialog
        PinInputDialog.show(this, "Enter PIN for ${profile.name}") { pin ->
            viewModel.selectProfile(profile.id, pin)
        }
    }
    
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    private fun showMessage(message: String) {
        // TODO: Show snackbar or toast
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }
    
    private fun showError(message: String) {
        // TODO: Show error dialog or snackbar
        android.widget.Toast.makeText(this, "Error: $message", android.widget.Toast.LENGTH_LONG).show()
    }
}

/**
 * Simple dialog for creating new profile
 */
object CreateProfileDialog {
    fun show(
        activity: AppCompatActivity,
        onProfileCreated: (name: String, isKidsProfile: Boolean) -> Unit
    ) {
        // TODO: Implement proper dialog
        // For now, create a simple profile
        onProfileCreated("New User", false)
    }
}

/**
 * Simple dialog for PIN input
 */
object PinInputDialog {
    fun show(
        activity: AppCompatActivity,
        title: String,
        onPinEntered: (pin: String) -> Unit
    ) {
        // TODO: Implement proper PIN dialog
        // For now, use empty PIN
        onPinEntered("")
    }
}
