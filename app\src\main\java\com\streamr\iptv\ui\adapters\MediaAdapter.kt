package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.streamr.iptv.R
import com.streamr.iptv.databinding.ItemMediaHorizontalBinding

class MediaAdapter(
    private val onItemClick: (MediaItem) -> Unit,
    private val onFavoriteClick: ((MediaItem) -> Unit)? = null
) : ListAdapter<MediaItem, MediaAdapter.MediaViewHolder>(MediaDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MediaViewHolder {
        val binding = ItemMediaHorizontalBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MediaViewHolder(binding, onItemClick, onFavoriteClick)
    }
    
    override fun onBindViewHolder(holder: MediaViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class MediaViewHolder(
        private val binding: ItemMediaHorizontalBinding,
        private val onItemClick: (MediaItem) -> Unit,
        private val onFavoriteClick: ((MediaItem) -> Unit)?
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: MediaItem) {
            binding.tvTitle.text = item.title
            
            // Load thumbnail image
            Glide.with(binding.root.context)
                .load(item.imageUrl)
                .placeholder(R.drawable.placeholder_media)
                .error(R.drawable.placeholder_media)
                .transition(DrawableTransitionOptions.withCrossFade())
                .into(binding.ivThumbnail)
            
            // Show/hide elements based on media type
            when (item.type) {
                MediaItem.Type.CHANNEL -> {
                    binding.tvLiveIndicator.visibility = View.VISIBLE
                    binding.progressBar.visibility = View.GONE
                }
                MediaItem.Type.MOVIE, MediaItem.Type.SERIES -> {
                    binding.tvLiveIndicator.visibility = View.GONE
                    if (item.progress > 0) {
                        binding.progressBar.visibility = View.VISIBLE
                        // Calculate progress percentage (assuming total duration is available)
                        // This is a simplified version - you'd need actual duration data
                        binding.progressBar.progress = (item.progress / 1000).toInt() // Convert to seconds
                    } else {
                        binding.progressBar.visibility = View.GONE
                    }
                }
            }
            
            // Show/hide favorite icon
            binding.ivFavorite.visibility = if (item.isFavorite) View.VISIBLE else View.GONE

            // Set click listeners
            binding.root.setOnClickListener {
                onItemClick(item)
            }

            // Set favorite click listener if provided
            onFavoriteClick?.let { favoriteCallback ->
                binding.ivFavorite.setOnClickListener {
                    favoriteCallback(item)
                }
            }
        }
    }
    
    private class MediaDiffCallback : DiffUtil.ItemCallback<MediaItem>() {
        override fun areItemsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem.id == newItem.id && oldItem.type == newItem.type
        }
        
        override fun areContentsTheSame(oldItem: MediaItem, newItem: MediaItem): Boolean {
            return oldItem == newItem
        }
    }
}
