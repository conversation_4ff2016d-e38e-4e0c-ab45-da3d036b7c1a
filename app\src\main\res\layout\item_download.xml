<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/primary_medium"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Thumbnail -->
        <ImageView
            android:id="@+id/ivThumbnail"
            android:layout_width="80dp"
            android:layout_height="60dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/image_placeholder"
            android:scaleType="centerCrop"
            tools:src="@drawable/image_placeholder" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title -->
            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="Movie Title"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="The Amazing Movie Title" />

            <!-- Quality and Size -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvQuality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/quality_badge"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="1080p"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvFileSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="2.5 GB"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <!-- Status -->
                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Downloading"
                    android:textColor="@color/accent_green"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="8dp"
                android:progress="45"
                android:progressTint="@color/accent_green"
                android:progressBackgroundTint="@color/primary_light" />

            <!-- Progress Info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvProgress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="45%"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvSpeed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="2.5 MB/s"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tvTimeRemaining"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="5 min left"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical">

            <!-- Play/Pause Button -->
            <ImageButton
                android:id="@+id/btnPlayPause"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_pause"
                app:tint="@color/accent_green" />

            <!-- More Options Button -->
            <ImageButton
                android:id="@+id/btnMore"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginTop="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_more_vert"
                app:tint="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
