package com.streamr.iptv.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * User profile entity
 */
@Entity(
    tableName = "user_profiles",
    indices = [Index(value = ["email"], unique = true)]
)
@Parcelize
data class UserProfile(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // Basic information
    val name: String,
    val email: String? = null,
    val avatarUrl: String? = null,
    val dateOfBirth: String? = null, // Format: yyyy-MM-dd
    val gender: Gender? = null,
    
    // Profile settings
    val isKidsProfile: Boolean = false,
    val parentalControlPin: String? = null,
    val maxRating: String = "R", // G, PG, PG-13, R, NC-17
    val language: String = "en",
    val country: String = "US",
    
    // Viewing preferences
    val preferredQuality: String = "HD",
    val autoPlay: Boolean = true,
    val skipIntros: Boolean = false,
    val subtitlesEnabled: Boolean = false,
    val preferredSubtitleLanguage: String? = null,
    val preferredAudioLanguage: String? = null,
    
    // Privacy settings
    val shareWatchHistory: Boolean = false,
    val allowRecommendations: Boolean = true,
    val allowNotifications: Boolean = true,
    
    // Account status
    val isActive: Boolean = true,
    val isPremium: Boolean = false,
    val subscriptionExpiresAt: Long? = null,
    
    // Timestamps
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val lastLoginAt: Long? = null
) : Parcelable {
    
    /**
     * Check if profile is for kids
     */
    fun isKidsProfile(): Boolean = isKidsProfile
    
    /**
     * Check if subscription is active
     */
    fun hasActiveSubscription(): Boolean {
        return isPremium && (subscriptionExpiresAt == null || subscriptionExpiresAt > System.currentTimeMillis())
    }
    
    /**
     * Get age from date of birth
     */
    fun getAge(): Int? {
        if (dateOfBirth.isNullOrEmpty()) return null
        
        return try {
            val birthDate = java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault())
                .parse(dateOfBirth)
            val now = java.util.Calendar.getInstance()
            val birth = java.util.Calendar.getInstance()
            birth.time = birthDate
            
            var age = now.get(java.util.Calendar.YEAR) - birth.get(java.util.Calendar.YEAR)
            if (now.get(java.util.Calendar.DAY_OF_YEAR) < birth.get(java.util.Calendar.DAY_OF_YEAR)) {
                age--
            }
            age
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * Check if content is appropriate for this profile
     */
    fun canViewContent(contentRating: String?): Boolean {
        if (contentRating.isNullOrEmpty()) return true
        
        val ratingOrder = listOf("G", "PG", "PG-13", "R", "NC-17")
        val maxRatingIndex = ratingOrder.indexOf(maxRating)
        val contentRatingIndex = ratingOrder.indexOf(contentRating)
        
        return contentRatingIndex <= maxRatingIndex
    }
}

/**
 * Gender enum
 */
enum class Gender {
    MALE, FEMALE, OTHER, PREFER_NOT_TO_SAY
}

/**
 * Watch history entity
 */
@Entity(
    tableName = "watch_history",
    foreignKeys = [
        ForeignKey(
            entity = UserProfile::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["mediaType", "mediaId"]),
        Index(value = ["watchedAt"])
    ]
)
@Parcelize
data class WatchHistory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val userId: Long,
    val mediaType: WatchMediaType,
    val mediaId: Long,
    val mediaTitle: String,
    val mediaThumbnail: String? = null,
    
    // Watch progress
    val watchedDuration: Long = 0, // Watched duration in milliseconds
    val totalDuration: Long = 0, // Total media duration in milliseconds
    val watchProgress: Float = 0f, // Progress percentage (0.0 - 1.0)
    val lastPosition: Long = 0, // Last playback position in milliseconds
    
    // Watch session info
    val watchedAt: Long = System.currentTimeMillis(),
    val deviceInfo: String? = null,
    val isCompleted: Boolean = false,
    
    // Additional metadata
    val seasonNumber: Int? = null,
    val episodeNumber: Int? = null,
    val channelName: String? = null
) : Parcelable {
    
    /**
     * Check if media was watched completely
     */
    fun isWatchedCompletely(): Boolean = isCompleted || watchProgress >= 0.9f
    
    /**
     * Get formatted watch progress
     */
    fun getFormattedProgress(): String = "${(watchProgress * 100).toInt()}%"
    
    /**
     * Get remaining duration
     */
    fun getRemainingDuration(): Long = totalDuration - watchedDuration
}

/**
 * Watch media types
 */
enum class WatchMediaType {
    CHANNEL, MOVIE, EPISODE, LIVE_RECORDING
}

/**
 * User favorites entity
 */
@Entity(
    tableName = "user_favorites",
    foreignKeys = [
        ForeignKey(
            entity = UserProfile::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["mediaType", "mediaId"]),
        Index(value = ["userId", "mediaType", "mediaId"], unique = true)
    ]
)
@Parcelize
data class UserFavorite(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val userId: Long,
    val mediaType: FavoriteMediaType,
    val mediaId: Long,
    val mediaTitle: String,
    val mediaThumbnail: String? = null,
    
    val addedAt: Long = System.currentTimeMillis(),
    
    // Additional metadata for different media types
    val channelNumber: String? = null,
    val genre: String? = null,
    val year: Int? = null,
    val rating: Float? = null
) : Parcelable

/**
 * Favorite media types
 */
enum class FavoriteMediaType {
    CHANNEL, MOVIE, SERIES, GENRE
}

/**
 * User watchlist entity
 */
@Entity(
    tableName = "user_watchlist",
    foreignKeys = [
        ForeignKey(
            entity = UserProfile::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["userId"]),
        Index(value = ["mediaType", "mediaId"]),
        Index(value = ["userId", "mediaType", "mediaId"], unique = true)
    ]
)
@Parcelize
data class UserWatchlist(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val userId: Long,
    val mediaType: WatchlistMediaType,
    val mediaId: Long,
    val mediaTitle: String,
    val mediaThumbnail: String? = null,
    val mediaDescription: String? = null,
    
    val addedAt: Long = System.currentTimeMillis(),
    val priority: Int = 0, // Higher number = higher priority
    
    // Notification settings
    val notifyOnAvailable: Boolean = false,
    val notifyOnNewEpisode: Boolean = false
) : Parcelable

/**
 * Watchlist media types
 */
enum class WatchlistMediaType {
    MOVIE, SERIES, CHANNEL
}

/**
 * User settings entity
 */
@Entity(
    tableName = "user_settings",
    foreignKeys = [
        ForeignKey(
            entity = UserProfile::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["userId"], unique = true)]
)
@Parcelize
data class UserSettings(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val userId: Long,
    
    // Playback settings
    val defaultQuality: String = "HD",
    val autoPlayNext: Boolean = true,
    val skipIntros: Boolean = false,
    val playbackSpeed: Float = 1.0f,
    
    // Audio/Video settings
    val preferredAudioLanguage: String = "en",
    val preferredSubtitleLanguage: String? = null,
    val subtitlesEnabled: Boolean = false,
    val audioBoost: Boolean = false,
    val nightMode: Boolean = false,
    
    // Interface settings
    val theme: AppTheme = AppTheme.DARK,
    val language: String = "en",
    val showAdultContent: Boolean = false,
    val gridSize: GridSize = GridSize.MEDIUM,
    
    // Download settings
    val downloadQuality: String = "HD",
    val downloadOnlyOnWifi: Boolean = true,
    val downloadOnlyWhenCharging: Boolean = false,
    val autoDeleteDownloads: Boolean = true,
    val downloadRetentionDays: Int = 30,
    
    // Notification settings
    val enableNotifications: Boolean = true,
    val notifyNewEpisodes: Boolean = true,
    val notifyDownloadComplete: Boolean = true,
    val notifyRecordingStart: Boolean = true,
    val quietHoursStart: String? = null, // Format: HH:mm
    val quietHoursEnd: String? = null, // Format: HH:mm
    
    // Privacy settings
    val shareWatchHistory: Boolean = false,
    val allowAnalytics: Boolean = true,
    val allowPersonalization: Boolean = true,
    
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable

/**
 * App themes
 */
enum class AppTheme {
    LIGHT, DARK, AUTO
}

/**
 * Grid sizes for content display
 */
enum class GridSize {
    SMALL, MEDIUM, LARGE
}
