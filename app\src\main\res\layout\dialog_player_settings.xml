<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Dialog Title -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Player Settings"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold" />

    <!-- Video Quality Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Video Quality"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <RadioGroup
        android:id="@+id/rgVideoQuality"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <RadioButton
            android:id="@+id/rbQualityAuto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="Auto (Recommended)"
            android:textColor="@color/text_primary" />

        <RadioButton
            android:id="@+id/rbQuality4K"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="4K (2160p)"
            android:textColor="@color/text_primary" />

        <RadioButton
            android:id="@+id/rbQuality1080p"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Full HD (1080p)"
            android:textColor="@color/text_primary" />

        <RadioButton
            android:id="@+id/rbQuality720p"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="HD (720p)"
            android:textColor="@color/text_primary" />

        <RadioButton
            android:id="@+id/rbQuality480p"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SD (480p)"
            android:textColor="@color/text_primary" />

        <RadioButton
            android:id="@+id/rbQuality360p"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Low (360p)"
            android:textColor="@color/text_primary" />

    </RadioGroup>

    <!-- Audio Track Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Audio Track"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <Spinner
        android:id="@+id/spinnerAudioTrack"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/spinner_background" />

    <!-- Subtitle Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Subtitles"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <Spinner
        android:id="@+id/spinnerSubtitle"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/spinner_background" />

    <!-- Playback Speed Section -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Playback Speed"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="0.5x"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

        <SeekBar
            android:id="@+id/seekBarSpeed"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:max="6"
            android:progress="2"
            android:progressTint="@color/accent_green"
            android:thumbTint="@color/accent_green" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="2.0x"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvSpeedValue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="Normal (1.0x)"
        android:textColor="@color/text_primary"
        android:textSize="14sp" />

    <!-- Advanced Settings -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="Advanced"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <CheckBox
        android:id="@+id/cbHardwareAcceleration"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:checked="true"
        android:text="Hardware Acceleration"
        android:textColor="@color/text_primary" />

    <CheckBox
        android:id="@+id/cbAdaptiveStreaming"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:checked="true"
        android:text="Adaptive Streaming"
        android:textColor="@color/text_primary" />

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btnCancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Cancel"
            android:textColor="@color/text_secondary" />

        <Button
            android:id="@+id/btnApply"
            style="@style/Widget.Material3.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/accent_green"
            android:text="Apply"
            android:textColor="@color/white" />

    </LinearLayout>

</LinearLayout>
