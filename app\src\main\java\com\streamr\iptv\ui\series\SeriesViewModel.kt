package com.streamr.iptv.ui.series

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class SeriesViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val series: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val playlists = repository.getActivePlaylists()
        addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                val firstPlaylist = playlistList.first()
                removeSource(playlists)
                addSource(repository.getSeriesByPlaylist(firstPlaylist.id)) { seriesList ->
                    value = seriesList?.map { MediaItem.fromSeries(it) } ?: emptyList()
                }
            } else {
                value = emptyList()
            }
        }
    }
    
    fun showSeries(seriesItem: MediaItem, context: Context) {
        viewModelScope.launch {
            try {
                // For now, play the first unwatched episode
                val episodes = repository.getEpisodesBySeries(seriesItem.id).value
                val nextEpisode = episodes?.firstOrNull { !it.isWatched } ?: episodes?.firstOrNull()
                nextEpisode?.let {
                    PlayerActivity.startWithEpisode(context, it, it.watchProgress)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
