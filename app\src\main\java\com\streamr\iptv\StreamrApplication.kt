package com.streamr.iptv

import android.app.Application
import androidx.lifecycle.lifecycleScope
import com.streamr.iptv.BuildConfig
import com.streamr.iptv.data.SampleDataProvider
import com.streamr.iptv.data.database.StreamrDatabase
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.launch

class StreamrApplication : Application() {
    
    val database by lazy { StreamrDatabase.getDatabase(this) }
    val repository by lazy { StreamrRepository(database) }
    
    override fun onCreate() {
        super.onCreate()
        instance = this

        // Add sample data for testing (only in debug builds)
        if (BuildConfig.DEBUG) {
            addSampleDataIfNeeded()
        }
    }

    private fun addSampleDataIfNeeded() {
        // This will run in a background thread
        Thread {
            try {
                val playlistDao = database.playlistDao()

                // Check if we already have data
                val existingPlaylists = playlistDao.getAllPlaylists().value
                if (existingPlaylists.isNullOrEmpty()) {
                    // Use test data with real streams for better testing
                    kotlinx.coroutines.runBlocking {
                        try {
                            // Try to use TestDataProvider if available (debug build)
                            val testDataClass = Class.forName("com.streamr.iptv.TestDataProvider")
                            val initMethod = testDataClass.getMethod("initializeTestData", repository::class.java)
                            initMethod.invoke(null, repository)
                        } catch (e: ClassNotFoundException) {
                            // Fallback to sample data if TestDataProvider not available (release build)
                            addFallbackSampleData()
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                // Fallback to sample data on any error
                addFallbackSampleData()
            }
        }.start()
    }

    private fun addFallbackSampleData() {
        try {
            val playlistDao = database.playlistDao()
            val channelDao = database.channelDao()
            val movieDao = database.movieDao()
            val seriesDao = database.seriesDao()
            val episodeDao = database.episodeDao()

            // Add sample playlist
            val samplePlaylist = SampleDataProvider.getSamplePlaylist()
            val playlistId = playlistDao.insertPlaylist(samplePlaylist)

            // Add sample channels
            val sampleChannels = SampleDataProvider.getSampleChannels(playlistId)
            channelDao.insertChannels(sampleChannels)

            // Add sample movies
            val sampleMovies = SampleDataProvider.getSampleMovies(playlistId)
            movieDao.insertMovies(sampleMovies)

            // Add sample series
            val sampleSeries = SampleDataProvider.getSampleSeries(playlistId)
            seriesDao.insertSeries(sampleSeries)

            // Add sample episodes
            sampleSeries.forEach { series ->
                val episodes = SampleDataProvider.getSampleEpisodes(series.id)
                if (episodes.isNotEmpty()) {
                    episodeDao.insertEpisodes(episodes)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    companion object {
        lateinit var instance: StreamrApplication
            private set
    }
}
