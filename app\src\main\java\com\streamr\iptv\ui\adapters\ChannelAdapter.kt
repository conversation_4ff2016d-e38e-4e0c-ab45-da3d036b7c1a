package com.streamr.iptv.ui.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.streamr.iptv.R
import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.databinding.ItemChannelBinding

class ChannelAdapter(
    private val onItemClick: (Channel) -> Unit,
    private val onFavoriteClick: ((Channel) -> Unit)? = null
) : ListAdapter<Channel, ChannelAdapter.ChannelViewHolder>(ChannelDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChannelViewHolder {
        val binding = ItemChannelBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ChannelViewHolder(binding, onItemClick, onFavoriteClick)
    }
    
    override fun onBindViewHolder(holder: ChannelViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class ChannelViewHolder(
        private val binding: ItemChannelBinding,
        private val onItemClick: (Channel) -> Unit,
        private val onFavoriteClick: ((Channel) -> Unit)?
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(channel: Channel) {
            binding.tvChannelName.text = channel.name
            binding.tvChannelGroup.text = channel.groupTitle ?: "General"
            
            // Load channel logo
            Glide.with(binding.root.context)
                .load(channel.logoUrl)
                .placeholder(R.drawable.placeholder_channel)
                .error(R.drawable.placeholder_channel)
                .into(binding.ivChannelLogo)
            
            // Show/hide favorite icon
            binding.ivFavorite.visibility = if (channel.isFavorite) View.VISIBLE else View.GONE

            // Set click listeners
            binding.root.setOnClickListener {
                onItemClick(channel)
            }

            // Set favorite click listener if provided
            onFavoriteClick?.let { favoriteCallback ->
                binding.ivFavorite.setOnClickListener {
                    favoriteCallback(channel)
                }
            }
        }
    }
    
    private class ChannelDiffCallback : DiffUtil.ItemCallback<Channel>() {
        override fun areItemsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: Channel, newItem: Channel): Boolean {
            return oldItem == newItem
        }
    }
}
