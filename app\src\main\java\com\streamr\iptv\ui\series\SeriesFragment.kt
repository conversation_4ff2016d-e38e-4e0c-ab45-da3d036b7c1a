package com.streamr.iptv.ui.series

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.streamr.iptv.databinding.FragmentSeriesBinding
import com.streamr.iptv.ui.adapters.MediaAdapter

class SeriesFragment : Fragment() {
    
    private var _binding: FragmentSeriesBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: SeriesViewModel by viewModels()
    private lateinit var seriesAdapter: MediaAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentSeriesBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        seriesAdapter = MediaAdapter(
            onItemClick = { series ->
                viewModel.showSeries(series, requireContext())
            },
            onFavoriteClick = { series ->
                viewModel.toggleFavorite(series)
            }
        )
        
        binding.rvSeries.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = seriesAdapter
        }
    }
    
    private fun observeViewModel() {
        viewModel.series.observe(viewLifecycleOwner) { series ->
            seriesAdapter.submitList(series)
            binding.emptyState.visibility = if (series.isEmpty()) View.VISIBLE else View.GONE
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
