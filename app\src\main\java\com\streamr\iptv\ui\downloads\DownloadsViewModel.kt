package com.streamr.iptv.ui.downloads

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.*
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.download.DownloadManager
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class DownloadsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    private val downloadManager = DownloadManager(application, repository)
    
    private val _downloadEvent = MutableLiveData<DownloadEvent>()
    val downloadEvent: LiveData<DownloadEvent> = _downloadEvent
    
    // Get all downloads and filter by status
    val allDownloads = downloadManager.getAllDownloads()
    
    val activeDownloads = allDownloads.map { downloads ->
        downloads.filter { it.isActive() }
    }
    
    val completedDownloads = allDownloads.map { downloads ->
        downloads.filter { it.isCompleted() }
    }
    
    val recordings = downloadManager.getAllRecordings()
    
    /**
     * Start a new download
     */
    fun startDownload(
        mediaId: Long,
        mediaType: DownloadMediaType,
        title: String,
        streamUrl: String,
        quality: DownloadQuality = DownloadQuality.HD_720P,
        description: String? = null,
        thumbnailUrl: String? = null
    ) {
        viewModelScope.launch {
            try {
                val result = downloadManager.startDownload(
                    mediaId, mediaType, title, streamUrl, quality, description, thumbnailUrl
                )
                
                if (result.isSuccess) {
                    _downloadEvent.value = DownloadEvent.DownloadStarted(result.getOrThrow(), title)
                } else {
                    // Handle error
                    val error = result.exceptionOrNull()?.message ?: "Unknown error"
                    _downloadEvent.value = DownloadEvent.DownloadFailed(0, title, error)
                }
            } catch (e: Exception) {
                _downloadEvent.value = DownloadEvent.DownloadFailed(0, title, e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Pause a download
     */
    fun pauseDownload(downloadId: Long) {
        viewModelScope.launch {
            try {
                downloadManager.pauseDownload(downloadId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Resume a download
     */
    fun resumeDownload(downloadId: Long) {
        viewModelScope.launch {
            try {
                downloadManager.resumeDownload(downloadId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Cancel a download
     */
    fun cancelDownload(downloadId: Long) {
        viewModelScope.launch {
            try {
                downloadManager.cancelDownload(downloadId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Delete a download
     */
    fun deleteDownload(downloadId: Long) {
        viewModelScope.launch {
            try {
                downloadManager.deleteDownload(downloadId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Retry a failed download
     */
    fun retryDownload(downloadId: Long) {
        viewModelScope.launch {
            try {
                // TODO: Implement retry logic
                // This would involve getting the original download info and starting a new download
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Pause all active downloads
     */
    fun pauseAllDownloads() {
        viewModelScope.launch {
            try {
                val active = activeDownloads.value ?: emptyList()
                active.filter { it.status == DownloadStatus.DOWNLOADING }.forEach { download ->
                    downloadManager.pauseDownload(download.id)
                }
                _downloadEvent.value = DownloadEvent.AllDownloadsPaused
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Resume all paused downloads
     */
    fun resumeAllDownloads() {
        viewModelScope.launch {
            try {
                val active = activeDownloads.value ?: emptyList()
                active.filter { 
                    it.status in listOf(DownloadStatus.PAUSED, DownloadStatus.PENDING, DownloadStatus.QUEUED) 
                }.forEach { download ->
                    downloadManager.resumeDownload(download.id)
                }
                _downloadEvent.value = DownloadEvent.AllDownloadsResumed
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Clear all completed downloads
     */
    fun clearCompletedDownloads() {
        viewModelScope.launch {
            try {
                val completed = completedDownloads.value ?: emptyList()
                completed.forEach { download ->
                    downloadManager.deleteDownload(download.id)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Play a downloaded file
     */
    fun playDownloadedFile(download: Download) {
        if (download.status == DownloadStatus.COMPLETED && !download.filePath.isNullOrEmpty()) {
            val context = getApplication<Application>()
            
            // Create a local file URI and play it
            when (download.mediaType) {
                DownloadMediaType.MOVIE -> {
                    // Play as movie
                    PlayerActivity.startWithLocalFile(
                        context,
                        download.filePath!!,
                        download.title,
                        download.description
                    )
                }
                DownloadMediaType.EPISODE -> {
                    // Play as episode
                    PlayerActivity.startWithLocalFile(
                        context,
                        download.filePath!!,
                        download.title,
                        download.description
                    )
                }
                DownloadMediaType.LIVE_RECORDING -> {
                    // Play as recording
                    PlayerActivity.startWithLocalFile(
                        context,
                        download.filePath!!,
                        download.title,
                        download.description
                    )
                }
            }
        }
    }
    
    /**
     * Schedule a recording
     */
    fun scheduleRecording(
        channelId: Long,
        programId: Long?,
        startTime: Long,
        endTime: Long,
        quality: DownloadQuality = DownloadQuality.HD_720P
    ) {
        viewModelScope.launch {
            try {
                val result = downloadManager.scheduleRecording(
                    channelId, programId, startTime, endTime, quality
                )
                
                if (result.isSuccess) {
                    // Recording scheduled successfully
                } else {
                    // Handle error
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Cancel a recording
     */
    fun cancelRecording(recordingId: Long) {
        viewModelScope.launch {
            try {
                downloadManager.cancelRecording(recordingId)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        downloadManager.cleanup()
    }
}
