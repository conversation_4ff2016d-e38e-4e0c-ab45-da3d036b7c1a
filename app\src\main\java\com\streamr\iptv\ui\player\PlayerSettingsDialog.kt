package com.streamr.iptv.ui.player

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.ArrayAdapter
import android.widget.SeekBar
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.Format
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector
import com.google.android.exoplayer2.trackselection.MappingTrackSelector
import com.streamr.iptv.R
import com.streamr.iptv.databinding.DialogPlayerSettingsBinding
import com.streamr.iptv.utils.PerformanceOptimizer

class PlayerSettingsDialog(
    context: Context,
    private val player: ExoPlayer,
    private val trackSelector: DefaultTrackSelector,
    private val onSettingsChanged: (PlayerSettings) -> Unit
) : Dialog(context, R.style.Theme_Streamr_Dialog) {
    
    private lateinit var binding: DialogPlayerSettingsBinding
    private var currentSettings = PlayerSettings()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogPlayerSettingsBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)
        
        setupUI()
        loadCurrentSettings()
        setupListeners()
    }
    
    private fun setupUI() {
        // Setup audio track spinner
        setupAudioTrackSpinner()
        
        // Setup subtitle spinner
        setupSubtitleSpinner()
        
        // Setup playback speed
        setupPlaybackSpeed()
        
        // Setup quality selection
        setupQualitySelection()
    }
    
    private fun setupAudioTrackSpinner() {
        val audioTracks = getAvailableAudioTracks()
        val adapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, audioTracks.map { it.displayName })
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerAudioTrack.adapter = adapter
    }
    
    private fun setupSubtitleSpinner() {
        val subtitleTracks = getAvailableSubtitleTracks()
        val adapter = ArrayAdapter(context, android.R.layout.simple_spinner_item, subtitleTracks.map { it.displayName })
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerSubtitle.adapter = adapter
    }
    
    private fun setupPlaybackSpeed() {
        val speeds = arrayOf(0.5f, 0.75f, 1.0f, 1.25f, 1.5f, 1.75f, 2.0f)
        
        binding.seekBarSpeed.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val speed = speeds[progress]
                binding.tvSpeedValue.text = when (speed) {
                    1.0f -> "Normal (1.0x)"
                    else -> "${speed}x"
                }
                currentSettings.playbackSpeed = speed
            }
            
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }
    
    private fun setupQualitySelection() {
        binding.rgVideoQuality.setOnCheckedChangeListener { _, checkedId ->
            currentSettings.videoQuality = when (checkedId) {
                R.id.rbQualityAuto -> VideoQualityOption.AUTO
                R.id.rbQuality4K -> VideoQualityOption.UHD_4K
                R.id.rbQuality1080p -> VideoQualityOption.FHD_1080P
                R.id.rbQuality720p -> VideoQualityOption.HD_720P
                R.id.rbQuality480p -> VideoQualityOption.SD_480P
                R.id.rbQuality360p -> VideoQualityOption.LD_360P
                else -> VideoQualityOption.AUTO
            }
        }
    }
    
    private fun setupListeners() {
        binding.btnCancel.setOnClickListener {
            dismiss()
        }
        
        binding.btnApply.setOnClickListener {
            applySettings()
            onSettingsChanged(currentSettings)
            dismiss()
        }
        
        binding.cbHardwareAcceleration.setOnCheckedChangeListener { _, isChecked ->
            currentSettings.enableHardwareAcceleration = isChecked
        }
        
        binding.cbAdaptiveStreaming.setOnCheckedChangeListener { _, isChecked ->
            currentSettings.enableAdaptiveStreaming = isChecked
        }
    }
    
    private fun loadCurrentSettings() {
        // Load current player settings
        val currentSpeed = player.playbackParameters.speed
        val speedIndex = when {
            currentSpeed <= 0.5f -> 0
            currentSpeed <= 0.75f -> 1
            currentSpeed <= 1.0f -> 2
            currentSpeed <= 1.25f -> 3
            currentSpeed <= 1.5f -> 4
            currentSpeed <= 1.75f -> 5
            else -> 6
        }
        binding.seekBarSpeed.progress = speedIndex
        
        // Load quality settings
        val trackSelectionParameters = trackSelector.parameters
        if (trackSelectionParameters.maxVideoWidth == Int.MAX_VALUE) {
            binding.rbQualityAuto.isChecked = true
        } else {
            // Determine current quality based on max resolution
            when {
                trackSelectionParameters.maxVideoHeight >= 2160 -> binding.rbQuality4K.isChecked = true
                trackSelectionParameters.maxVideoHeight >= 1080 -> binding.rbQuality1080p.isChecked = true
                trackSelectionParameters.maxVideoHeight >= 720 -> binding.rbQuality720p.isChecked = true
                trackSelectionParameters.maxVideoHeight >= 480 -> binding.rbQuality480p.isChecked = true
                else -> binding.rbQuality360p.isChecked = true
            }
        }
        
        // Load advanced settings
        val config = PerformanceOptimizer.getExoPlayerConfig(context)
        binding.cbHardwareAcceleration.isChecked = config.enableHardwareAcceleration
        binding.cbAdaptiveStreaming.isChecked = config.enableAdaptiveStreaming
    }
    
    private fun applySettings() {
        // Apply video quality
        val parametersBuilder = trackSelector.buildUponParameters()
        
        when (currentSettings.videoQuality) {
            VideoQualityOption.AUTO -> {
                parametersBuilder.setMaxVideoSizeSd()
            }
            VideoQualityOption.UHD_4K -> {
                parametersBuilder.setMaxVideoSize(3840, 2160)
            }
            VideoQualityOption.FHD_1080P -> {
                parametersBuilder.setMaxVideoSize(1920, 1080)
            }
            VideoQualityOption.HD_720P -> {
                parametersBuilder.setMaxVideoSize(1280, 720)
            }
            VideoQualityOption.SD_480P -> {
                parametersBuilder.setMaxVideoSize(854, 480)
            }
            VideoQualityOption.LD_360P -> {
                parametersBuilder.setMaxVideoSize(640, 360)
            }
        }
        
        trackSelector.setParameters(parametersBuilder)
        
        // Apply playback speed
        player.setPlaybackSpeed(currentSettings.playbackSpeed)
        
        // Apply audio track
        val selectedAudioIndex = binding.spinnerAudioTrack.selectedItemPosition
        if (selectedAudioIndex >= 0) {
            // Apply audio track selection
            // This would require more complex track selection logic
        }
        
        // Apply subtitle track
        val selectedSubtitleIndex = binding.spinnerSubtitle.selectedItemPosition
        if (selectedSubtitleIndex >= 0) {
            // Apply subtitle track selection
            // This would require more complex track selection logic
        }
    }
    
    private fun getAvailableAudioTracks(): List<TrackInfo> {
        val tracks = mutableListOf<TrackInfo>()
        tracks.add(TrackInfo("Default", "Default Audio"))
        
        try {
            val mappedTrackInfo = trackSelector.currentMappedTrackInfo
            if (mappedTrackInfo != null) {
                for (rendererIndex in 0 until mappedTrackInfo.rendererCount) {
                    if (mappedTrackInfo.getRendererType(rendererIndex) == com.google.android.exoplayer2.C.TRACK_TYPE_AUDIO) {
                        val trackGroups = mappedTrackInfo.getTrackGroups(rendererIndex)
                        for (groupIndex in 0 until trackGroups.length) {
                            val trackGroup = trackGroups[groupIndex]
                            for (trackIndex in 0 until trackGroup.length) {
                                val format = trackGroup.getFormat(trackIndex)
                                val displayName = buildString {
                                    append(format.language ?: "Unknown")
                                    if (format.channelCount > 0) {
                                        append(" (${format.channelCount} ch)")
                                    }
                                }
                                tracks.add(TrackInfo(format.id ?: "", displayName))
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return tracks
    }
    
    private fun getAvailableSubtitleTracks(): List<TrackInfo> {
        val tracks = mutableListOf<TrackInfo>()
        tracks.add(TrackInfo("off", "Off"))
        
        try {
            val mappedTrackInfo = trackSelector.currentMappedTrackInfo
            if (mappedTrackInfo != null) {
                for (rendererIndex in 0 until mappedTrackInfo.rendererCount) {
                    if (mappedTrackInfo.getRendererType(rendererIndex) == com.google.android.exoplayer2.C.TRACK_TYPE_TEXT) {
                        val trackGroups = mappedTrackInfo.getTrackGroups(rendererIndex)
                        for (groupIndex in 0 until trackGroups.length) {
                            val trackGroup = trackGroups[groupIndex]
                            for (trackIndex in 0 until trackGroup.length) {
                                val format = trackGroup.getFormat(trackIndex)
                                val displayName = format.language ?: "Unknown"
                                tracks.add(TrackInfo(format.id ?: "", displayName))
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return tracks
    }
}

data class PlayerSettings(
    var videoQuality: VideoQualityOption = VideoQualityOption.AUTO,
    var playbackSpeed: Float = 1.0f,
    var selectedAudioTrack: String? = null,
    var selectedSubtitleTrack: String? = null,
    var enableHardwareAcceleration: Boolean = true,
    var enableAdaptiveStreaming: Boolean = true
)

enum class VideoQualityOption {
    AUTO, UHD_4K, FHD_1080P, HD_720P, SD_480P, LD_360P
}

data class TrackInfo(
    val id: String,
    val displayName: String
)
