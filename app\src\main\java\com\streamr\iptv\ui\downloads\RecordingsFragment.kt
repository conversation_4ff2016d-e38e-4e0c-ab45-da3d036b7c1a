package com.streamr.iptv.ui.downloads

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.streamr.iptv.databinding.FragmentDownloadListBinding

class RecordingsFragment : Fragment() {
    
    private var _binding: FragmentDownloadListBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: DownloadsViewModel by viewModels({ requireParentFragment() })
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDownloadListBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupRecyclerView()
        observeViewModel()
    }
    
    private fun setupRecyclerView() {
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            // TODO: Setup recordings adapter
        }
    }
    
    private fun observeViewModel() {
        viewModel.recordings.observe(viewLifecycleOwner) { recordings ->
            // TODO: Update recordings list
            
            // Show/hide empty state
            if (recordings.isEmpty()) {
                binding.recyclerView.visibility = View.GONE
                binding.emptyState.visibility = View.VISIBLE
                binding.tvEmptyMessage.text = "No recordings"
            } else {
                binding.recyclerView.visibility = View.VISIBLE
                binding.emptyState.visibility = View.GONE
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
