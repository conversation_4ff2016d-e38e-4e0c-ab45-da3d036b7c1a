package com.streamr.iptv.ui.player

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.PlaybackException
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.source.hls.HlsMediaSource
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource
import com.google.android.exoplayer2.DefaultLoadControl
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection
import com.streamr.iptv.databinding.ActivityPlayerBinding
import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.data.model.Movie
import com.streamr.iptv.data.model.Episode
import com.streamr.iptv.utils.PerformanceOptimizer
import com.streamr.iptv.utils.ErrorHandler

class PlayerActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityPlayerBinding
    private val viewModel: PlayerViewModel by viewModels()
    
    private var exoPlayer: ExoPlayer? = null
    private var trackSelector: DefaultTrackSelector? = null
    private var isFullscreen = false
    private var playWhenReady = true
    private var currentPosition = 0L
    
    // Media information
    private var mediaId: Long = -1
    private var mediaType: MediaType = MediaType.CHANNEL
    private var mediaTitle: String = ""
    private var streamUrl: String = ""
    
    enum class MediaType {
        CHANNEL, MOVIE, EPISODE
    }
    
    companion object {
        private const val EXTRA_MEDIA_ID = "media_id"
        private const val EXTRA_MEDIA_TYPE = "media_type"
        private const val EXTRA_MEDIA_TITLE = "media_title"
        private const val EXTRA_STREAM_URL = "stream_url"
        private const val EXTRA_START_POSITION = "start_position"
        
        fun startWithChannel(context: Context, channel: Channel) {
            val intent = Intent(context, PlayerActivity::class.java).apply {
                putExtra(EXTRA_MEDIA_ID, channel.id)
                putExtra(EXTRA_MEDIA_TYPE, MediaType.CHANNEL.name)
                putExtra(EXTRA_MEDIA_TITLE, channel.name)
                putExtra(EXTRA_STREAM_URL, channel.streamUrl)
            }
            context.startActivity(intent)
        }
        
        fun startWithMovie(context: Context, movie: Movie, startPosition: Long = 0) {
            val intent = Intent(context, PlayerActivity::class.java).apply {
                putExtra(EXTRA_MEDIA_ID, movie.id)
                putExtra(EXTRA_MEDIA_TYPE, MediaType.MOVIE.name)
                putExtra(EXTRA_MEDIA_TITLE, movie.name)
                putExtra(EXTRA_STREAM_URL, movie.streamUrl)
                putExtra(EXTRA_START_POSITION, startPosition)
            }
            context.startActivity(intent)
        }
        
        fun startWithEpisode(context: Context, episode: Episode, startPosition: Long = 0) {
            val intent = Intent(context, PlayerActivity::class.java).apply {
                putExtra(EXTRA_MEDIA_ID, episode.id)
                putExtra(EXTRA_MEDIA_TYPE, MediaType.EPISODE.name)
                putExtra(EXTRA_MEDIA_TITLE, episode.name)
                putExtra(EXTRA_STREAM_URL, episode.streamUrl)
                putExtra(EXTRA_START_POSITION, startPosition)
            }
            context.startActivity(intent)
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPlayerBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Keep screen on during playback
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // Set landscape orientation for better viewing experience
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        
        extractIntentData()
        setupUI()
        initializePlayer()
        observeViewModel()
    }
    
    private fun extractIntentData() {
        mediaId = intent.getLongExtra(EXTRA_MEDIA_ID, -1)
        mediaType = MediaType.valueOf(intent.getStringExtra(EXTRA_MEDIA_TYPE) ?: MediaType.CHANNEL.name)
        mediaTitle = intent.getStringExtra(EXTRA_MEDIA_TITLE) ?: ""
        streamUrl = intent.getStringExtra(EXTRA_STREAM_URL) ?: ""
        currentPosition = intent.getLongExtra(EXTRA_START_POSITION, 0)
    }
    
    private fun setupUI() {
        binding.tvMediaTitle.text = mediaTitle
        
        // Back button
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        // Fullscreen toggle
        binding.btnFullscreen.setOnClickListener {
            toggleFullscreen()
        }
        
        // Player view click to show/hide controls
        binding.playerView.setOnClickListener {
            toggleControlsVisibility()
        }

        // Settings button click
        binding.playerView.findViewById<View>(R.id.btnPlayerSettings)?.setOnClickListener {
            showPlayerSettings()
        }
        
        // Initially hide system UI for immersive experience
        hideSystemUI()
    }
    
    private fun initializePlayer() {
        // Get optimized configuration
        val config = PerformanceOptimizer.getExoPlayerConfig(this)

        // Create track selector with adaptive streaming
        val trackSelector = DefaultTrackSelector(this).apply {
            if (config.enableAdaptiveStreaming) {
                setParameters(
                    buildUponParameters()
                        .setMaxVideoBitrate(if (config.maxBitrate > 0) config.maxBitrate else Int.MAX_VALUE)
                        .build()
                )
            }
        }

        // Store track selector for settings dialog
        this.trackSelector = trackSelector

        // Create load control with optimized buffer settings
        val loadControl = DefaultLoadControl.Builder()
            .setBufferDurationsMs(
                config.bufferConfig.minBufferMs,
                config.bufferConfig.maxBufferMs,
                config.bufferConfig.bufferForPlaybackMs,
                config.bufferConfig.bufferForPlaybackAfterRebufferMs
            )
            .build()

        exoPlayer = ExoPlayer.Builder(this)
            .setTrackSelector(trackSelector)
            .setLoadControl(loadControl)
            .build().also { player ->
            binding.playerView.player = player

            // Create media source
            val dataSourceFactory = DefaultHttpDataSource.Factory()
                .setUserAgent("StreamrIPTV/1.0")
                .setConnectTimeoutMs(15000)
                .setReadTimeoutMs(15000)
            
            val mediaSource = if (streamUrl.contains(".m3u8")) {
                // HLS stream
                HlsMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(streamUrl))
            } else {
                // Regular stream
                MediaItem.fromUri(streamUrl)
            }
            
            // Set media source
            if (mediaSource is MediaItem) {
                player.setMediaItem(mediaSource)
            } else {
                player.setMediaSource(mediaSource as com.google.android.exoplayer2.source.MediaSource)
            }
            
            // Set playback position if resuming
            if (currentPosition > 0) {
                player.seekTo(currentPosition)
            }
            
            player.playWhenReady = playWhenReady
            player.prepare()
            
            // Add listener for playback events
            player.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(playbackState: Int) {
                    when (playbackState) {
                        Player.STATE_BUFFERING -> {
                            binding.progressBar.visibility = View.VISIBLE
                        }
                        Player.STATE_READY -> {
                            binding.progressBar.visibility = View.GONE
                            // Update watch history
                            updateWatchHistory()
                        }
                        Player.STATE_ENDED -> {
                            if (mediaType == MediaType.MOVIE || mediaType == MediaType.EPISODE) {
                                // Mark as watched and finish
                                markAsWatched()
                                finish()
                            }
                        }
                    }
                }
                
                override fun onPlayerError(error: PlaybackException) {
                    binding.progressBar.visibility = View.GONE
                    binding.tvError.visibility = View.VISIBLE

                    // Use ErrorHandler for user-friendly error messages
                    val errorMessage = ErrorHandler.handlePlaybackError(this@PlayerActivity, error)
                    binding.tvError.text = errorMessage

                    // Log error report for debugging
                    val errorReport = ErrorHandler.createErrorReport(
                        this@PlayerActivity,
                        error,
                        "Playing media: $mediaTitle",
                        mapOf(
                            "media_type" to mediaType.name,
                            "media_id" to mediaId.toString(),
                            "stream_url" to streamUrl
                        )
                    )
                    android.util.Log.e("PlayerActivity", errorReport.toLogString())
                }
            })
        }
    }
    
    private fun toggleFullscreen() {
        isFullscreen = !isFullscreen
        if (isFullscreen) {
            hideSystemUI()
            binding.controlsContainer.visibility = View.GONE
        } else {
            showSystemUI()
            binding.controlsContainer.visibility = View.VISIBLE
        }
    }
    
    private fun toggleControlsVisibility() {
        if (binding.controlsContainer.visibility == View.VISIBLE) {
            binding.controlsContainer.visibility = View.GONE
        } else {
            binding.controlsContainer.visibility = View.VISIBLE
            // Auto-hide controls after 3 seconds
            binding.controlsContainer.postDelayed({
                if (binding.controlsContainer.visibility == View.VISIBLE) {
                    binding.controlsContainer.visibility = View.GONE
                }
            }, 3000)
        }
    }
    
    private fun hideSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        WindowInsetsControllerCompat(window, binding.root).let { controller ->
            controller.hide(WindowInsetsCompat.Type.systemBars())
            controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
    }
    
    private fun showSystemUI() {
        WindowCompat.setDecorFitsSystemWindows(window, true)
        WindowInsetsControllerCompat(window, binding.root).show(WindowInsetsCompat.Type.systemBars())
    }
    
    private fun updateWatchHistory() {
        when (mediaType) {
            MediaType.CHANNEL -> viewModel.updateChannelWatchHistory(mediaId)
            MediaType.MOVIE -> viewModel.updateMovieWatchProgress(mediaId, exoPlayer?.currentPosition ?: 0)
            MediaType.EPISODE -> viewModel.updateEpisodeWatchProgress(mediaId, exoPlayer?.currentPosition ?: 0)
        }
    }
    
    private fun markAsWatched() {
        when (mediaType) {
            MediaType.MOVIE -> viewModel.markMovieAsWatched(mediaId)
            MediaType.EPISODE -> viewModel.markEpisodeAsWatched(mediaId)
            else -> { /* Channels don't have watched status */ }
        }
    }
    
    private fun showPlayerSettings() {
        exoPlayer?.let { player ->
            trackSelector?.let { selector ->
                val settingsDialog = PlayerSettingsDialog(
                    this,
                    player,
                    selector
                ) { settings ->
                    // Settings applied callback
                    // Additional settings handling can be added here
                }
                settingsDialog.show()
            }
        }
    }

    private fun observeViewModel() {
        // Observe any ViewModel data if needed
    }
    
    override fun onStart() {
        super.onStart()
        if (exoPlayer == null) {
            initializePlayer()
        }
    }
    
    override fun onResume() {
        super.onResume()
        exoPlayer?.playWhenReady = playWhenReady
    }
    
    override fun onPause() {
        super.onPause()
        exoPlayer?.let {
            playWhenReady = it.playWhenReady
            currentPosition = it.currentPosition
            it.playWhenReady = false
        }
        
        // Save current position for movies and episodes
        if (mediaType != MediaType.CHANNEL) {
            updateWatchHistory()
        }
    }
    
    override fun onStop() {
        super.onStop()
        releasePlayer()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        releasePlayer()
    }
    
    private fun releasePlayer() {
        exoPlayer?.let { player ->
            playWhenReady = player.playWhenReady
            currentPosition = player.currentPosition
            player.release()
        }
        exoPlayer = null
    }
}
