package com.streamr.iptv.utils

import android.app.ActivityManager
import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Performance optimization utilities for Streamr IPTV
 */
object PerformanceOptimizer {
    
    /**
     * Check if device has sufficient memory for smooth playback
     */
    fun hasEnoughMemory(context: Context): <PERSON>olean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        // Require at least 100MB of available memory
        val requiredMemoryMB = 100 * 1024 * 1024L
        return memoryInfo.availMem > requiredMemoryMB
    }
    
    /**
     * Get recommended video quality based on network speed
     */
    suspend fun getRecommendedQuality(context: Context): VideoQuality = withContext(Dispatchers.IO) {
        val networkSpeed = getNetworkSpeed(context)
        
        when {
            networkSpeed >= 25_000_000 -> VideoQuality.UHD_4K // 25 Mbps+
            networkSpeed >= 8_000_000 -> VideoQuality.FHD_1080P // 8 Mbps+
            networkSpeed >= 3_000_000 -> VideoQuality.HD_720P // 3 Mbps+
            networkSpeed >= 1_000_000 -> VideoQuality.SD_480P // 1 Mbps+
            else -> VideoQuality.LD_360P
        }
    }
    
    /**
     * Estimate network speed (simplified)
     */
    private fun getNetworkSpeed(context: Context): Long {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            capabilities?.let {
                return when {
                    it.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                        // WiFi - assume good speed
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            it.linkDownstreamBandwidthKbps * 1000L
                        } else {
                            50_000_000L // 50 Mbps default for WiFi
                        }
                    }
                    it.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                        // Cellular - estimate based on type
                        when {
                            it.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> {
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                                    it.linkDownstreamBandwidthKbps * 1000L
                                } else {
                                    10_000_000L // 10 Mbps default for cellular
                                }
                            }
                            else -> 5_000_000L // 5 Mbps conservative estimate
                        }
                    }
                    else -> 1_000_000L // 1 Mbps very conservative
                }
            }
        }
        
        return 5_000_000L // 5 Mbps default fallback
    }
    
    /**
     * Check if device supports hardware acceleration
     */
    fun supportsHardwareAcceleration(context: Context): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val configurationInfo = activityManager.deviceConfigurationInfo
            configurationInfo.reqGlEsVersion >= 0x20000 // OpenGL ES 2.0+
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get optimal buffer size based on device capabilities
     */
    fun getOptimalBufferSize(context: Context): BufferConfig {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryClass = activityManager.memoryClass
        
        return when {
            memoryClass >= 512 -> BufferConfig.HIGH_PERFORMANCE
            memoryClass >= 256 -> BufferConfig.BALANCED
            memoryClass >= 128 -> BufferConfig.MEMORY_EFFICIENT
            else -> BufferConfig.LOW_MEMORY
        }
    }
    
    /**
     * Check network connectivity and type
     */
    fun getNetworkInfo(context: Context): NetworkInfo {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            capabilities?.let {
                return when {
                    it.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkInfo.WIFI
                    it.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkInfo.CELLULAR
                    it.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkInfo.ETHERNET
                    else -> NetworkInfo.UNKNOWN
                }
            }
        }
        
        return NetworkInfo.UNKNOWN
    }
    
    /**
     * Check if device is in low power mode
     */
    fun isInLowPowerMode(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
            powerManager.isPowerSaveMode
        } else {
            false
        }
    }
    
    /**
     * Get recommended ExoPlayer settings
     */
    fun getExoPlayerConfig(context: Context): ExoPlayerConfig {
        val hasEnoughMemory = hasEnoughMemory(context)
        val supportsHardware = supportsHardwareAcceleration(context)
        val bufferConfig = getOptimalBufferSize(context)
        val isLowPower = isInLowPowerMode(context)
        
        return ExoPlayerConfig(
            enableHardwareAcceleration = supportsHardware && !isLowPower,
            bufferConfig = if (isLowPower) BufferConfig.LOW_MEMORY else bufferConfig,
            enableAdaptiveStreaming = hasEnoughMemory,
            maxBitrate = if (isLowPower) 2_000_000 else -1, // 2 Mbps limit in low power mode
            enableAudioOffload = supportsHardware && hasEnoughMemory
        )
    }
}

/**
 * Video quality levels
 */
enum class VideoQuality(val displayName: String, val maxBitrate: Int) {
    LD_360P("360p", 1_000_000),
    SD_480P("480p", 2_500_000),
    HD_720P("720p", 5_000_000),
    FHD_1080P("1080p", 8_000_000),
    UHD_4K("4K", 25_000_000)
}

/**
 * Buffer configuration levels
 */
enum class BufferConfig(
    val minBufferMs: Int,
    val maxBufferMs: Int,
    val bufferForPlaybackMs: Int,
    val bufferForPlaybackAfterRebufferMs: Int
) {
    LOW_MEMORY(15000, 30000, 2500, 5000),
    MEMORY_EFFICIENT(30000, 60000, 2500, 5000),
    BALANCED(50000, 120000, 2500, 5000),
    HIGH_PERFORMANCE(50000, 180000, 2500, 5000)
}

/**
 * Network type information
 */
enum class NetworkInfo {
    WIFI,
    CELLULAR,
    ETHERNET,
    UNKNOWN
}

/**
 * ExoPlayer configuration
 */
data class ExoPlayerConfig(
    val enableHardwareAcceleration: Boolean,
    val bufferConfig: BufferConfig,
    val enableAdaptiveStreaming: Boolean,
    val maxBitrate: Int, // -1 for no limit
    val enableAudioOffload: Boolean
)
