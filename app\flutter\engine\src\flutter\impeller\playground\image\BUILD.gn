# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/impeller/tools/impeller.gni")

impeller_component("image") {
  testonly = true
  public = [
    "compressed_image.h",
    "decompressed_image.h",
  ]

  sources = [
    "compressed_image.cc",
    "decompressed_image.cc",
  ]

  public_deps = [
    "../../base",
    "../../geometry",
  ]

  deps = [ "//flutter/fml" ]
}

impeller_component("image_skia_backend") {
  testonly = true
  public = [ "backends/skia/compressed_image_skia.h" ]

  sources = [ "backends/skia/compressed_image_skia.cc" ]

  public_deps = [
    ":image",
    "../../base",
    "../../geometry",
  ]

  deps = [
    "//flutter/fml",
    "//flutter/skia",
  ]
}
