package com.streamr.iptv.ui.downloads

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayoutMediator
import com.streamr.iptv.databinding.FragmentDownloadsBinding
import com.streamr.iptv.ui.adapters.DownloadAdapter
import com.streamr.iptv.data.model.Download
import com.streamr.iptv.data.model.DownloadStatus

class DownloadsFragment : Fragment() {
    
    private var _binding: FragmentDownloadsBinding? = null
    private val binding get() = _binding!!
    
    private val viewModel: DownloadsViewModel by viewModels()
    private lateinit var downloadsAdapter: DownloadAdapter
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentDownloadsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupUI()
        setupTabs()
        observeViewModel()
    }
    
    private fun setupUI() {
        // Setup downloads adapter
        downloadsAdapter = DownloadAdapter(
            onPlayPauseClick = { download ->
                handlePlayPauseClick(download)
            },
            onMoreClick = { download ->
                showDownloadOptions(download)
            },
            onItemClick = { download ->
                handleDownloadClick(download)
            }
        )
        
        // Setup settings button
        binding.btnDownloadSettings.setOnClickListener {
            showDownloadSettings()
        }
        
        // Setup control buttons
        binding.btnPauseAll.setOnClickListener {
            viewModel.pauseAllDownloads()
        }
        
        binding.btnResumeAll.setOnClickListener {
            viewModel.resumeAllDownloads()
        }
        
        binding.btnClearCompleted.setOnClickListener {
            viewModel.clearCompletedDownloads()
        }
    }
    
    private fun setupTabs() {
        val adapter = DownloadsPagerAdapter(this)
        binding.viewPager.adapter = adapter
        
        TabLayoutMediator(binding.tabLayout, binding.viewPager) { tab, position ->
            tab.text = when (position) {
                0 -> "Active"
                1 -> "Completed"
                2 -> "Recordings"
                else -> "Tab $position"
            }
        }.attach()
    }
    
    private fun observeViewModel() {
        // Observe active downloads
        viewModel.activeDownloads.observe(viewLifecycleOwner) { downloads ->
            updateDownloadControls(downloads)
        }
        
        // Observe download events
        viewModel.downloadEvent.observe(viewLifecycleOwner) { event ->
            when (event) {
                is DownloadEvent.DownloadStarted -> {
                    showMessage("Download started: ${event.title}")
                }
                is DownloadEvent.DownloadCompleted -> {
                    showMessage("Download completed: ${event.title}")
                }
                is DownloadEvent.DownloadFailed -> {
                    showMessage("Download failed: ${event.title}")
                }
                is DownloadEvent.AllDownloadsPaused -> {
                    showMessage("All downloads paused")
                }
                is DownloadEvent.AllDownloadsResumed -> {
                    showMessage("All downloads resumed")
                }
            }
        }
    }
    
    private fun handlePlayPauseClick(download: Download) {
        when (download.status) {
            DownloadStatus.DOWNLOADING -> {
                viewModel.pauseDownload(download.id)
            }
            DownloadStatus.PAUSED, DownloadStatus.PENDING, DownloadStatus.QUEUED -> {
                viewModel.resumeDownload(download.id)
            }
            DownloadStatus.COMPLETED -> {
                // Play the downloaded file
                viewModel.playDownloadedFile(download)
            }
            DownloadStatus.FAILED, DownloadStatus.CANCELLED -> {
                // Retry download
                viewModel.retryDownload(download.id)
            }
            else -> {
                // Do nothing for other statuses
            }
        }
    }
    
    private fun showDownloadOptions(download: Download) {
        val options = mutableListOf<String>()
        
        when (download.status) {
            DownloadStatus.DOWNLOADING, DownloadStatus.PAUSED, DownloadStatus.QUEUED -> {
                options.add("Cancel Download")
                options.add("Change Quality")
            }
            DownloadStatus.COMPLETED -> {
                options.add("Play")
                options.add("Delete")
                options.add("Share")
                options.add("Details")
            }
            DownloadStatus.FAILED, DownloadStatus.CANCELLED -> {
                options.add("Retry")
                options.add("Delete")
            }
            else -> {
                options.add("Delete")
            }
        }
        
        // Show options dialog
        showOptionsDialog(download, options)
    }
    
    private fun handleDownloadClick(download: Download) {
        when (download.status) {
            DownloadStatus.COMPLETED -> {
                // Play the downloaded file
                viewModel.playDownloadedFile(download)
            }
            else -> {
                // Show download details
                showDownloadDetails(download)
            }
        }
    }
    
    private fun updateDownloadControls(downloads: List<Download>) {
        val hasActiveDownloads = downloads.any { it.isActive() }
        val hasCompletedDownloads = downloads.any { it.isCompleted() }
        
        binding.downloadControls.visibility = if (hasActiveDownloads || hasCompletedDownloads) {
            View.VISIBLE
        } else {
            View.GONE
        }
        
        binding.btnPauseAll.isEnabled = downloads.any { it.status == DownloadStatus.DOWNLOADING }
        binding.btnResumeAll.isEnabled = downloads.any { 
            it.status in listOf(DownloadStatus.PAUSED, DownloadStatus.PENDING, DownloadStatus.QUEUED) 
        }
        binding.btnClearCompleted.isEnabled = hasCompletedDownloads
    }
    
    private fun showDownloadSettings() {
        // TODO: Show download settings dialog
        showMessage("Download settings coming soon")
    }
    
    private fun showOptionsDialog(download: Download, options: List<String>) {
        // TODO: Show options dialog
        showMessage("Options: ${options.joinToString(", ")}")
    }
    
    private fun showDownloadDetails(download: Download) {
        // TODO: Show download details dialog
        showMessage("Download details for: ${download.title}")
    }
    
    private fun showMessage(message: String) {
        // TODO: Show snackbar or toast
        android.widget.Toast.makeText(context, message, android.widget.Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}

// Download events for UI updates
sealed class DownloadEvent {
    data class DownloadStarted(val id: Long, val title: String) : DownloadEvent()
    data class DownloadCompleted(val id: Long, val title: String) : DownloadEvent()
    data class DownloadFailed(val id: Long, val title: String, val error: String) : DownloadEvent()
    object AllDownloadsPaused : DownloadEvent()
    object AllDownloadsResumed : DownloadEvent()
}
