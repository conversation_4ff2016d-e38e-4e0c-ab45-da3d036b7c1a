package com.streamr.iptv.data

import com.streamr.iptv.data.model.*

object SampleDataProvider {
    
    fun getSamplePlaylist(): Playlist {
        return Playlist(
            id = 1,
            name = "Sample IPTV Playlist",
            type = PlaylistType.M3U_PLAYLIST,
            serverUrl = "https://example.com/playlist.m3u8",
            isActive = true
        )
    }
    
    fun getSampleChannels(playlistId: Long): List<Channel> {
        return listOf(
            Channel(
                id = 1,
                playlistId = playlistId,
                name = "BBC News",
                streamUrl = "https://d2vnbkvjbims7j.cloudfront.net/containerA/LTN/playlist.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/6/62/BBC_News_2019.svg",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = true,
                language = "English",
                country = "UK"
            ),
            Channel(
                id = 2,
                playlistId = playlistId,
                name = "Al Jazeera English",
                streamUrl = "https://live-hls-web-aje.getaj.net/AJE/01.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/en/f/f2/Aljazeera_eng.png",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = true,
                language = "English",
                country = "Qatar"
            ),
            Channel(
                id = 3,
                playlistId = playlistId,
                name = "NASA TV",
                streamUrl = "https://ntv1.akamaized.net/hls/live/2014075/NASA-NTV1-HLS/master.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/e/e5/NASA_logo.svg",
                groupTitle = "Documentary",
                category = ChannelCategory.DOCUMENTARY,
                isHD = true,
                language = "English",
                country = "USA"
            ),
            Channel(
                id = 4,
                playlistId = playlistId,
                name = "Red Bull TV",
                streamUrl = "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/en/d/d4/Red_Bull_TV_logo.png",
                groupTitle = "Sports",
                category = ChannelCategory.SPORTS,
                isHD = true,
                language = "English",
                country = "Austria"
            ),
            Channel(
                id = 5,
                playlistId = playlistId,
                name = "Euronews",
                streamUrl = "https://rakuten-euronews-1-gb.samsung.wurl.com/manifest/playlist.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/0/0b/Euronews_2016_logo.svg",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = true,
                language = "English",
                country = "France"
            )
        )
    }
    
    fun getSampleMovies(playlistId: Long): List<Movie> {
        return listOf(
            Movie(
                id = 1,
                playlistId = playlistId,
                name = "Big Buck Bunny",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                posterUrl = "https://peach.blender.org/wp-content/uploads/bbb-splash.png",
                description = "Big Buck Bunny tells the story of a giant rabbit with a heart bigger than himself.",
                genre = "Animation, Comedy",
                year = 2008,
                rating = 7.2f,
                duration = 10 // 10 minutes
            ),
            Movie(
                id = 2,
                playlistId = playlistId,
                name = "Elephant Dream",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                posterUrl = "https://orange.blender.org/wp-content/themes/orange/images/media/gallery/s1_proog.jpg",
                description = "The first open movie from Blender Foundation.",
                genre = "Animation, Sci-Fi",
                year = 2006,
                rating = 6.8f,
                duration = 11 // 11 minutes
            ),
            Movie(
                id = 3,
                playlistId = playlistId,
                name = "Sintel",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4",
                posterUrl = "https://durian.blender.org/wp-content/uploads/2010/06/sintel_trailer_1080p.png",
                description = "A lonely young woman, Sintel, helps and befriends a dragon.",
                genre = "Animation, Adventure",
                year = 2010,
                rating = 7.5f,
                duration = 15 // 15 minutes
            )
        )
    }
    
    fun getSampleSeries(playlistId: Long): List<Series> {
        return listOf(
            Series(
                id = 1,
                playlistId = playlistId,
                name = "Sample Series 1",
                posterUrl = "https://via.placeholder.com/300x450/019863/FFFFFF?text=Series+1",
                description = "A sample series for testing purposes.",
                genre = "Drama",
                year = 2023,
                rating = 8.0f,
                totalSeasons = 2,
                totalEpisodes = 20
            ),
            Series(
                id = 2,
                playlistId = playlistId,
                name = "Sample Series 2",
                posterUrl = "https://via.placeholder.com/300x450/8ecdb7/000000?text=Series+2",
                description = "Another sample series for testing.",
                genre = "Comedy",
                year = 2023,
                rating = 7.5f,
                totalSeasons = 1,
                totalEpisodes = 10
            )
        )
    }
    
    fun getSampleEpisodes(seriesId: Long): List<Episode> {
        return when (seriesId) {
            1L -> listOf(
                Episode(
                    id = 1,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 1,
                    name = "Pilot Episode",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                    description = "The first episode of the series.",
                    duration = 45
                ),
                Episode(
                    id = 2,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 2,
                    name = "Second Episode",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                    description = "The second episode continues the story.",
                    duration = 42
                )
            )
            2L -> listOf(
                Episode(
                    id = 3,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 1,
                    name = "Comedy Pilot",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4",
                    description = "A funny start to the comedy series.",
                    duration = 30
                )
            )
            else -> emptyList()
        }
    }
}
