<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- ExoPlayer View -->
    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        app:controller_layout_id="@layout/custom_player_controls"
        app:resize_mode="fit"
        app:show_buffering="when_playing"
        app:use_controller="true" />

    <!-- Loading Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminateTint="@color/accent_green"
        android:visibility="gone" />

    <!-- Error Message -->
    <TextView
        android:id="@+id/tvError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/edittext_background"
        android:padding="16dp"
        android:text="Unable to play this stream"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:visibility="gone" />

    <!-- Custom Controls Overlay -->
    <LinearLayout
        android:id="@+id/controlsContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Top Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_top_overlay"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <!-- Back Button -->
            <ImageView
                android:id="@+id/btnBack"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_arrow_back"
                app:tint="@color/white" />

            <!-- Media Title -->
            <TextView
                android:id="@+id/tvMediaTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="Media Title"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Fullscreen Button -->
            <ImageView
                android:id="@+id/btnFullscreen"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                android:src="@drawable/ic_fullscreen"
                app:tint="@color/white" />

        </LinearLayout>

        <!-- Spacer to push bottom controls down -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Bottom Controls (if needed for additional controls) -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_bottom_overlay"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="16dp"
            android:visibility="gone">

            <!-- Additional controls can be added here -->

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
