package com.streamr.iptv.data.repository

import androidx.lifecycle.LiveData
import com.streamr.iptv.data.database.*
import com.streamr.iptv.data.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StreamrRepository(private val database: StreamrDatabase) {
    
    private val playlistDao = database.playlistDao()
    private val channelDao = database.channelDao()
    private val movieDao = database.movieDao()
    private val seriesDao = database.seriesDao()
    private val episodeDao = database.episodeDao()
    
    // Playlist operations
    fun getAllPlaylists(): LiveData<List<Playlist>> = playlistDao.getAllPlaylists()
    
    fun getActivePlaylists(): LiveData<List<Playlist>> = playlistDao.getActivePlaylists()
    
    suspend fun getPlaylistById(id: Long): Playlist? = withContext(Dispatchers.IO) {
        playlistDao.getPlaylistById(id)
    }
    
    suspend fun insertPlaylist(playlist: Playlist): Long = withContext(Dispatchers.IO) {
        playlistDao.insertPlaylist(playlist)
    }
    
    suspend fun updatePlaylist(playlist: Playlist) = withContext(Dispatchers.IO) {
        playlistDao.updatePlaylist(playlist)
    }
    
    suspend fun deletePlaylist(playlist: Playlist) = withContext(Dispatchers.IO) {
        // Delete all related data first
        channelDao.deleteChannelsByPlaylist(playlist.id)
        movieDao.deleteMoviesByPlaylist(playlist.id)
        seriesDao.deleteSeriesByPlaylist(playlist.id)
        // Then delete the playlist
        playlistDao.deletePlaylist(playlist)
    }
    
    suspend fun updatePlaylistLastUsed(id: Long) = withContext(Dispatchers.IO) {
        playlistDao.updateLastUsed(id)
    }
    
    // Channel operations
    fun getChannelsByPlaylist(playlistId: Long): LiveData<List<Channel>> = 
        channelDao.getChannelsByPlaylist(playlistId)
    
    fun getChannelsByCategory(playlistId: Long, category: ChannelCategory): LiveData<List<Channel>> = 
        channelDao.getChannelsByCategory(playlistId, category)
    
    fun getFavoriteChannels(): LiveData<List<Channel>> = channelDao.getFavoriteChannels()
    
    fun getRecentlyWatchedChannels(): LiveData<List<Channel>> = channelDao.getRecentlyWatchedChannels()
    
    fun searchChannels(query: String): LiveData<List<Channel>> = channelDao.searchChannels(query)
    
    suspend fun insertChannels(channels: List<Channel>) = withContext(Dispatchers.IO) {
        channelDao.insertChannels(channels)
    }
    
    suspend fun updateChannel(channel: Channel) = withContext(Dispatchers.IO) {
        channelDao.updateChannel(channel)
    }
    
    suspend fun updateChannelWatchHistory(id: Long) = withContext(Dispatchers.IO) {
        channelDao.updateWatchHistory(id)
    }
    
    suspend fun updateChannelFavoriteStatus(id: Long, isFavorite: Boolean) = withContext(Dispatchers.IO) {
        channelDao.updateFavoriteStatus(id, isFavorite)
    }

    suspend fun getChannelById(id: Long): Channel? = withContext(Dispatchers.IO) {
        channelDao.getChannelById(id)
    }
    
    // Movie operations
    fun getMoviesByPlaylist(playlistId: Long): LiveData<List<Movie>> = 
        movieDao.getMoviesByPlaylist(playlistId)
    
    fun getFavoriteMovies(): LiveData<List<Movie>> = movieDao.getFavoriteMovies()
    
    fun getRecentlyWatchedMovies(): LiveData<List<Movie>> = movieDao.getRecentlyWatchedMovies()
    
    fun getRecentlyAddedMovies(): LiveData<List<Movie>> {
        val weekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
        return movieDao.getRecentlyAddedMovies(weekAgo)
    }
    
    fun searchMovies(query: String): LiveData<List<Movie>> = movieDao.searchMovies(query)
    
    suspend fun insertMovies(movies: List<Movie>) = withContext(Dispatchers.IO) {
        movieDao.insertMovies(movies)
    }
    
    suspend fun updateMovie(movie: Movie) = withContext(Dispatchers.IO) {
        movieDao.updateMovie(movie)
    }
    
    suspend fun updateMovieWatchProgress(id: Long, progress: Long) = withContext(Dispatchers.IO) {
        movieDao.updateWatchProgress(id, progress)
    }
    
    suspend fun updateMovieFavoriteStatus(id: Long, isFavorite: Boolean) = withContext(Dispatchers.IO) {
        movieDao.updateFavoriteStatus(id, isFavorite)
    }

    suspend fun markMovieAsWatched(id: Long) = withContext(Dispatchers.IO) {
        movieDao.markAsWatched(id)
    }

    suspend fun getMovieById(id: Long): Movie? = withContext(Dispatchers.IO) {
        movieDao.getMovieById(id)
    }
    
    // Series operations
    fun getSeriesByPlaylist(playlistId: Long): LiveData<List<Series>> = 
        seriesDao.getSeriesByPlaylist(playlistId)
    
    fun getFavoriteSeries(): LiveData<List<Series>> = seriesDao.getFavoriteSeries()
    
    fun getRecentlyWatchedSeries(): LiveData<List<Series>> = seriesDao.getRecentlyWatchedSeries()
    
    fun searchSeries(query: String): LiveData<List<Series>> = seriesDao.searchSeries(query)
    
    suspend fun insertSeries(series: List<Series>) = withContext(Dispatchers.IO) {
        seriesDao.insertSeries(series)
    }
    
    suspend fun updateSeries(series: Series) = withContext(Dispatchers.IO) {
        seriesDao.updateSeries(series)
    }
    
    suspend fun updateSeriesFavoriteStatus(id: Long, isFavorite: Boolean) = withContext(Dispatchers.IO) {
        seriesDao.updateFavoriteStatus(id, isFavorite)
    }
    
    // Episode operations
    fun getEpisodesBySeries(seriesId: Long): LiveData<List<Episode>> = 
        episodeDao.getEpisodesBySeries(seriesId)
    
    fun getEpisodesBySeason(seriesId: Long, season: Int): LiveData<List<Episode>> = 
        episodeDao.getEpisodesBySeason(seriesId, season)
    
    suspend fun insertEpisodes(episodes: List<Episode>) = withContext(Dispatchers.IO) {
        episodeDao.insertEpisodes(episodes)
    }
    
    suspend fun updateEpisode(episode: Episode) = withContext(Dispatchers.IO) {
        episodeDao.updateEpisode(episode)
    }
    
    suspend fun updateEpisodeWatchProgress(id: Long, progress: Long) = withContext(Dispatchers.IO) {
        episodeDao.updateWatchProgress(id, progress)
    }

    suspend fun markEpisodeAsWatched(id: Long) = withContext(Dispatchers.IO) {
        episodeDao.markAsWatched(id)
    }

    // ==================== User Management ====================

    suspend fun insertUserProfile(profile: UserProfile): Long = withContext(Dispatchers.IO) {
        dao.insertUserProfile(profile)
    }

    suspend fun updateUserProfile(profile: UserProfile) = withContext(Dispatchers.IO) {
        dao.updateUserProfile(profile)
    }

    suspend fun deleteUserProfile(profileId: Long) = withContext(Dispatchers.IO) {
        dao.deleteUserProfile(profileId)
    }

    suspend fun getUserProfileById(profileId: Long): UserProfile? = withContext(Dispatchers.IO) {
        dao.getUserProfileById(profileId)
    }

    suspend fun getAllUserProfiles(): List<UserProfile> = withContext(Dispatchers.IO) {
        dao.getAllUserProfiles()
    }

    suspend fun updateUserLastLogin(profileId: Long, timestamp: Long) = withContext(Dispatchers.IO) {
        dao.updateUserLastLogin(profileId, timestamp)
    }

    // ==================== User Settings ====================

    suspend fun insertUserSettings(settings: UserSettings): Long = withContext(Dispatchers.IO) {
        dao.insertUserSettings(settings)
    }

    suspend fun updateUserSettings(settings: UserSettings) = withContext(Dispatchers.IO) {
        dao.updateUserSettings(settings)
    }

    suspend fun getUserSettings(userId: Long): UserSettings? = withContext(Dispatchers.IO) {
        dao.getUserSettings(userId)
    }

    // ==================== Watch History ====================

    suspend fun insertWatchHistory(history: WatchHistory): Long = withContext(Dispatchers.IO) {
        dao.insertWatchHistory(history)
    }

    fun getUserWatchHistory(userId: Long): LiveData<List<WatchHistory>> =
        dao.getUserWatchHistory(userId)

    suspend fun deleteWatchHistory(historyId: Long) = withContext(Dispatchers.IO) {
        dao.deleteWatchHistory(historyId)
    }

    // ==================== User Favorites ====================

    suspend fun insertUserFavorite(favorite: UserFavorite): Long = withContext(Dispatchers.IO) {
        dao.insertUserFavorite(favorite)
    }

    suspend fun removeUserFavorite(userId: Long, mediaType: FavoriteMediaType, mediaId: Long) = withContext(Dispatchers.IO) {
        dao.removeUserFavorite(userId, mediaType, mediaId)
    }

    fun getUserFavorites(userId: Long): LiveData<List<UserFavorite>> =
        dao.getUserFavorites(userId)

    suspend fun isInUserFavorites(userId: Long, mediaType: FavoriteMediaType, mediaId: Long): Boolean = withContext(Dispatchers.IO) {
        dao.isInUserFavorites(userId, mediaType, mediaId)
    }

    // ==================== User Watchlist ====================

    suspend fun insertUserWatchlist(watchlist: UserWatchlist): Long = withContext(Dispatchers.IO) {
        dao.insertUserWatchlist(watchlist)
    }

    suspend fun removeUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long) = withContext(Dispatchers.IO) {
        dao.removeUserWatchlist(userId, mediaType, mediaId)
    }

    fun getUserWatchlist(userId: Long): LiveData<List<UserWatchlist>> =
        dao.getUserWatchlist(userId)

    suspend fun isInUserWatchlist(userId: Long, mediaType: WatchlistMediaType, mediaId: Long): Boolean = withContext(Dispatchers.IO) {
        dao.isInUserWatchlist(userId, mediaType, mediaId)
    }
}
