package com.streamr.iptv.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * EPG Program entity
 */
@Entity(
    tableName = "epg_programs",
    foreignKeys = [
        ForeignKey(
            entity = Channel::class,
            parentColumns = ["id"],
            childColumns = ["channelId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index(value = ["channelId"]),
        Index(value = ["startTime"]),
        Index(value = ["endTime"]),
        Index(value = ["channelId", "startTime"])
    ]
)
@Parcelize
data class EPGProgram(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val channelId: Long,
    val title: String,
    val description: String? = null,
    val category: String? = null,
    val startTime: Long, // Unix timestamp
    val endTime: Long, // Unix timestamp
    val duration: Int, // Duration in minutes
    
    // Additional metadata
    val episodeTitle: String? = null,
    val seasonNumber: Int? = null,
    val episodeNumber: Int? = null,
    val year: Int? = null,
    val rating: String? = null,
    val director: String? = null,
    val cast: String? = null,
    val genre: String? = null,
    val language: String? = null,
    val country: String? = null,
    
    // Images
    val posterUrl: String? = null,
    val thumbnailUrl: String? = null,
    
    // Flags
    val isLive: Boolean = false,
    val isPremiere: Boolean = false,
    val isRepeat: Boolean = false,
    val hasSubtitles: Boolean = false,
    val isHD: Boolean = false,
    
    // User interaction
    val isReminder: Boolean = false,
    val isRecorded: Boolean = false,
    
    // Timestamps
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * Check if program is currently airing
     */
    fun isCurrentlyAiring(): Boolean {
        val now = System.currentTimeMillis()
        return now >= startTime && now <= endTime
    }
    
    /**
     * Check if program is upcoming (starts within next hour)
     */
    fun isUpcoming(): Boolean {
        val now = System.currentTimeMillis()
        val oneHour = 60 * 60 * 1000L
        return startTime > now && startTime <= now + oneHour
    }
    
    /**
     * Get progress percentage (0-100)
     */
    fun getProgressPercentage(): Int {
        if (!isCurrentlyAiring()) return 0
        
        val now = System.currentTimeMillis()
        val totalDuration = endTime - startTime
        val elapsed = now - startTime
        
        return ((elapsed.toDouble() / totalDuration.toDouble()) * 100).toInt()
    }
    
    /**
     * Get remaining time in minutes
     */
    fun getRemainingMinutes(): Int {
        if (!isCurrentlyAiring()) return 0
        
        val now = System.currentTimeMillis()
        val remaining = endTime - now
        
        return (remaining / (60 * 1000)).toInt()
    }
    
    /**
     * Format start time for display
     */
    fun getFormattedStartTime(): String {
        val date = Date(startTime)
        val formatter = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Format date for display
     */
    fun getFormattedDate(): String {
        val date = Date(startTime)
        val formatter = java.text.SimpleDateFormat("MMM dd", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Get time range string (e.g., "14:30 - 16:00")
     */
    fun getTimeRange(): String {
        val startDate = Date(startTime)
        val endDate = Date(endTime)
        val formatter = java.text.SimpleDateFormat("HH:mm", java.util.Locale.getDefault())
        return "${formatter.format(startDate)} - ${formatter.format(endDate)}"
    }
}

/**
 * EPG data for a specific channel and date
 */
data class ChannelEPGData(
    val channel: Channel,
    val date: String, // Format: yyyy-MM-dd
    val programs: List<EPGProgram>
) {
    /**
     * Get current program
     */
    fun getCurrentProgram(): EPGProgram? {
        return programs.find { it.isCurrentlyAiring() }
    }
    
    /**
     * Get next program
     */
    fun getNextProgram(): EPGProgram? {
        val now = System.currentTimeMillis()
        return programs
            .filter { it.startTime > now }
            .minByOrNull { it.startTime }
    }
    
    /**
     * Get programs for specific time range
     */
    fun getProgramsInRange(startTime: Long, endTime: Long): List<EPGProgram> {
        return programs.filter { program ->
            program.startTime < endTime && program.endTime > startTime
        }
    }
}

/**
 * EPG reminder entity
 */
@Entity(
    tableName = "epg_reminders",
    foreignKeys = [
        ForeignKey(
            entity = EPGProgram::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["programId"]), Index(value = ["reminderTime"])]
)
@Parcelize
data class EPGReminder(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val programId: Long,
    val reminderTime: Long, // When to show reminder (Unix timestamp)
    val reminderType: ReminderType = ReminderType.NOTIFICATION,
    val isActive: Boolean = true,
    val isTriggered: Boolean = false,
    
    val createdAt: Long = System.currentTimeMillis()
) : Parcelable

/**
 * Types of reminders
 */
enum class ReminderType {
    NOTIFICATION,    // Show notification
    AUTO_TUNE,      // Automatically tune to channel
    RECORD          // Start recording
}

/**
 * EPG recording entity
 */
@Entity(
    tableName = "epg_recordings",
    foreignKeys = [
        ForeignKey(
            entity = EPGProgram::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["programId"]), Index(value = ["status"])]
)
@Parcelize
data class EPGRecording(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val programId: Long,
    val recordingPath: String? = null,
    val status: RecordingStatus = RecordingStatus.SCHEDULED,
    val quality: String? = null,
    val fileSize: Long = 0,
    
    val scheduledAt: Long = System.currentTimeMillis(),
    val startedAt: Long? = null,
    val completedAt: Long? = null
) : Parcelable

/**
 * Recording status
 */
enum class RecordingStatus {
    SCHEDULED,      // Scheduled for recording
    IN_PROGRESS,    // Currently recording
    COMPLETED,      // Recording completed successfully
    FAILED,         // Recording failed
    CANCELLED       // Recording cancelled by user
}

/**
 * EPG category for organizing programs
 */
@Entity(tableName = "epg_categories")
@Parcelize
data class EPGCategory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val name: String,
    val displayName: String,
    val color: String? = null,
    val iconUrl: String? = null,
    val sortOrder: Int = 0,
    val isVisible: Boolean = true
) : Parcelable

/**
 * EPG search result
 */
data class EPGSearchResult(
    val program: EPGProgram,
    val channel: Channel,
    val matchType: SearchMatchType
)

/**
 * Types of search matches
 */
enum class SearchMatchType {
    TITLE,          // Match in program title
    DESCRIPTION,    // Match in program description
    CAST,           // Match in cast
    DIRECTOR,       // Match in director
    CATEGORY        // Match in category/genre
}
