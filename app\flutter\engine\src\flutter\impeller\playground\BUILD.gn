# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/impeller/tools/impeller.gni")
import("//flutter/testing/testing.gni")

impeller_component("playground") {
  testonly = true

  sources = [
    "playground.cc",
    "playground.h",
    "playground_impl.cc",
    "playground_impl.h",
    "switches.cc",
    "switches.h",
    "widgets.cc",
    "widgets.h",

    # Swiftshader is Vulkan backend specific but the utilities are not.
    "backend/vulkan/swiftshader_utilities.cc",
    "backend/vulkan/swiftshader_utilities.h",
  ]

  deps = [ "../renderer/backend" ]

  if (impeller_enable_metal) {
    sources += [
      "backend/metal/playground_impl_mtl.h",
      "backend/metal/playground_impl_mtl.mm",
    ]
  }

  if (impeller_enable_opengles) {
    sources += [
      "backend/gles/playground_impl_gles.cc",
      "backend/gles/playground_impl_gles.h",
    ]
  }

  if (impeller_enable_vulkan) {
    sources += [
      "backend/vulkan/playground_impl_vk.cc",
      "backend/vulkan/playground_impl_vk.h",
    ]
  }

  public_deps = [
    "../fixtures:modern_shader_fixtures",
    "../fixtures:shader_fixtures",
    "../renderer",
    "image:image_skia_backend",
    "imgui:imgui_impeller_backend",
    "//flutter/fml",
    "//flutter/testing:testing_lib",
    "//flutter/third_party/glfw",
    "//flutter/third_party/imgui:imgui_glfw",
  ]

  if (impeller_supports_rendering) {
    public_deps += [
      "../entity:entity_shaders",
      "../entity:framebuffer_blend_entity_shaders",
      "../entity:modern_entity_shaders",
    ]
  }

  if (is_mac) {
    frameworks = [
      "AppKit.framework",
      "QuartzCore.framework",
    ]
  }
}

impeller_component("playground_test") {
  testonly = true

  sources = [
    "compute_playground_test.cc",
    "compute_playground_test.h",
    "playground_test.cc",
    "playground_test.h",
  ]

  public_deps = [
    ":playground",
    "//flutter/testing:testing_lib",
  ]
}
