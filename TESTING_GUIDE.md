# 🧪 دليل اختبار تطبيق Streamr IPTV

## 📋 قائمة الاختبارات الأساسية

### 🎬 اختبار مشغل الوسائط (PlayerActivity)

#### ✅ اختبارات القنوات المباشرة
- [ ] تشغيل قناة من القائمة الرئيسية
- [ ] تشغيل قناة من قسم Live TV
- [ ] اختبار التحكم في التشغيل (تشغيل/إيقاف)
- [ ] اختبار وضع ملء الشاشة
- [ ] اختبار التقديم والإرجاع (للقنوات المدعومة)
- [ ] اختبار تغيير الاتجاه (عمودي/أفقي)

#### ✅ اختبارات الأفلام
- [ ] تشغيل فيلم من قسم Movies
- [ ] حفظ تقدم المشاهدة
- [ ] استكمال المشاهدة من النقطة المحفوظة
- [ ] وضع علامة "تم المشاهدة" عند الانتهاء
- [ ] اختبار جودة الفيديو المختلفة

#### ✅ اختبارات المسلسلات
- [ ] تشغيل حلقة من مسلسل
- [ ] الانتقال التلقائي للحلقة التالية
- [ ] حفظ تقدم المشاهدة للحلقات
- [ ] عرض معلومات الحلقة والموسم

### 🌐 اختبار تكامل Xtream Codes

#### ✅ اختبار المصادقة
- [ ] إضافة خادم Xtream Codes صحيح
- [ ] اختبار بيانات اعتماد خاطئة
- [ ] اختبار انتهاء صلاحية الحساب
- [ ] اختبار اتصال الشبكة الضعيف

#### ✅ اختبار تحميل المحتوى
- [ ] تحميل قائمة القنوات المباشرة
- [ ] تحميل قائمة الأفلام
- [ ] تحميل قائمة المسلسلات والحلقات
- [ ] اختبار الفئات والتصنيفات

### 📄 اختبار محلل M3U

#### ✅ اختبار ملفات M3U
- [ ] تحليل ملف M3U من رابط
- [ ] استخراج معلومات القنوات
- [ ] التعامل مع الشعارات والأيقونات
- [ ] اختبار ملفات M3U معطوبة

### 🔍 اختبار نظام البحث

#### ✅ اختبار البحث
- [ ] البحث في القنوات
- [ ] البحث في الأفلام
- [ ] البحث في المسلسلات
- [ ] البحث بالكلمات المفتاحية
- [ ] اختبار النتائج الفارغة

### ⚙️ اختبار إدارة قوائم التشغيل

#### ✅ اختبار إضافة قوائم التشغيل
- [ ] إضافة قائمة Xtream Codes
- [ ] إضافة قائمة M3U
- [ ] التحقق من صحة البيانات
- [ ] اختبار الحقول المطلوبة

#### ✅ اختبار تحرير قوائم التشغيل
- [ ] تحرير معلومات القائمة
- [ ] تحديث بيانات الاعتماد
- [ ] حفظ التغييرات
- [ ] إلغاء التحرير

#### ✅ اختبار حذف قوائم التشغيل
- [ ] حذف قائمة تشغيل
- [ ] حذف البيانات المرتبطة
- [ ] تأكيد الحذف

### 💖 اختبار نظام المفضلة

#### ✅ اختبار إضافة/إزالة المفضلة
- [ ] إضافة قناة للمفضلة
- [ ] إضافة فيلم للمفضلة
- [ ] إضافة مسلسل للمفضلة
- [ ] إزالة من المفضلة
- [ ] عرض قائمة المفضلة

### 📱 اختبار واجهة المستخدم

#### ✅ اختبار التنقل
- [ ] التنقل بين الأقسام الرئيسية
- [ ] استخدام شريط التنقل السفلي
- [ ] اختبار الأزرار والقوائم
- [ ] اختبار التمرير والقوائم

#### ✅ اختبار الاستجابة
- [ ] اختبار على شاشات مختلفة الأحجام
- [ ] اختبار الاتجاه العمودي والأفقي
- [ ] اختبار سرعة الاستجابة
- [ ] اختبار استهلاك الذاكرة

## 🐛 اختبارات الأخطاء الشائعة

### ⚠️ اختبارات الشبكة
- [ ] انقطاع الاتصال أثناء التشغيل
- [ ] بطء الاتصال
- [ ] خطأ في الخادم (500, 404)
- [ ] انتهاء مهلة الاتصال

### ⚠️ اختبارات البيانات
- [ ] ملفات فيديو معطوبة
- [ ] روابط منتهية الصلاحية
- [ ] بيانات JSON غير صحيحة
- [ ] ملفات M3U فارغة

### ⚠️ اختبارات الأداء
- [ ] استهلاك البطارية
- [ ] استهلاك البيانات
- [ ] استهلاك الذاكرة
- [ ] سرعة بدء التشغيل

## 📊 معايير النجاح

### ✅ معايير الأداء
- وقت بدء التشغيل: أقل من 3 ثوانٍ
- وقت تحميل القوائم: أقل من 5 ثوانٍ
- استهلاك الذاكرة: أقل من 200 MB
- استقرار التطبيق: عدم تعطل لأكثر من 30 دقيقة

### ✅ معايير الجودة
- جودة الفيديو: دعم حتى 4K
- جودة الصوت: دعم Dolby Digital
- استقرار التشغيل: عدم انقطاع لأكثر من 10 ثوانٍ
- دقة البيانات: 99% من البيانات صحيحة

## 🔧 أدوات الاختبار المقترحة

### 📱 اختبار الأجهزة
- أجهزة Android مختلفة (Samsung, Huawei, Xiaomi)
- إصدارات Android مختلفة (API 21+)
- أحجام شاشات مختلفة
- قوة معالجة مختلفة

### 🌐 اختبار الشبكة
- WiFi سريع (100+ Mbps)
- WiFi بطيء (5-10 Mbps)
- بيانات الجوال (4G/5G)
- VPN مفعل/معطل

### 📊 أدوات المراقبة
- Android Studio Profiler
- Firebase Crashlytics
- Network Monitor
- Battery Historian

## 📝 تقرير الاختبار

### 📋 نموذج تقرير الخطأ
```
العنوان: [وصف مختصر للخطأ]
الوصف: [وصف تفصيلي للمشكلة]
خطوات الإعادة: [الخطوات لإعادة إنتاج الخطأ]
النتيجة المتوقعة: [ما كان يجب أن يحدث]
النتيجة الفعلية: [ما حدث فعلاً]
الجهاز: [نوع الجهاز وإصدار Android]
الشدة: [منخفضة/متوسطة/عالية/حرجة]
```

### 📈 مؤشرات الأداء
- معدل نجاح الاختبارات: ____%
- عدد الأخطاء المكتشفة: ____
- عدد الأخطاء المصححة: ____
- تقييم الأداء العام: ____/10
