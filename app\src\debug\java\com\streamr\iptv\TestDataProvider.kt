package com.streamr.iptv

import com.streamr.iptv.data.model.*

/**
 * Test data provider for debugging and testing purposes
 * Contains real IPTV streams for testing functionality
 */
object TestDataProvider {
    
    /**
     * Get test playlists with real working streams
     */
    fun getTestPlaylists(): List<Playlist> {
        return listOf(
            // Free IPTV M3U playlist
            Playlist(
                id = 100,
                name = "Free IPTV Channels",
                type = PlaylistType.M3U_PLAYLIST,
                serverUrl = "https://iptv-org.github.io/iptv/index.m3u",
                isActive = true,
                isRemembered = true
            ),
            
            // Demo Xtream Codes (replace with real credentials for testing)
            Playlist(
                id = 101,
                name = "Demo Xtream Server",
                type = PlaylistType.XTREAM_CODES,
                username = "demo",
                password = "demo",
                serverUrl = "http://demo.xtream-codes.com:25461",
                isActive = false,
                isRemembered = false
            )
        )
    }
    
    /**
     * Get test channels with real working streams
     */
    fun getTestChannels(playlistId: Long): List<Channel> {
        return listOf(
            // BBC News (usually works)
            Channel(
                id = 200,
                playlistId = playlistId,
                name = "BBC News",
                streamUrl = "https://d2vnbkvjbims7j.cloudfront.net/containerA/LTN/playlist.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/6/62/BBC_News_2019.svg",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = true,
                language = "English",
                country = "UK"
            ),
            
            // Al Jazeera English
            Channel(
                id = 201,
                playlistId = playlistId,
                name = "Al Jazeera English",
                streamUrl = "https://live-hls-web-aje.getaj.net/AJE/01.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/en/f/f2/Aljazeera_eng.png",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = true,
                language = "English",
                country = "Qatar"
            ),
            
            // NASA TV
            Channel(
                id = 202,
                playlistId = playlistId,
                name = "NASA TV",
                streamUrl = "https://ntv1.akamaized.net/hls/live/2014075/NASA-NTV1-HLS/master.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/e/e5/NASA_logo.svg",
                groupTitle = "Documentary",
                category = ChannelCategory.DOCUMENTARY,
                isHD = true,
                language = "English",
                country = "USA"
            ),
            
            // Red Bull TV
            Channel(
                id = 203,
                playlistId = playlistId,
                name = "Red Bull TV",
                streamUrl = "https://rbmn-live.akamaized.net/hls/live/590964/BoRB-AT/master.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/en/d/d4/Red_Bull_TV_logo.png",
                groupTitle = "Sports",
                category = ChannelCategory.SPORTS,
                isHD = true,
                language = "English",
                country = "Austria"
            ),
            
            // France 24 English
            Channel(
                id = 204,
                playlistId = playlistId,
                name = "France 24 English",
                streamUrl = "https://static.france24.com/live/F24_EN_LO_HLS/live_web.m3u8",
                logoUrl = "https://upload.wikimedia.org/wikipedia/commons/8/8a/France24.png",
                groupTitle = "News",
                category = ChannelCategory.NEWS,
                isHD = false,
                language = "English",
                country = "France"
            )
        )
    }
    
    /**
     * Get test movies with sample video files
     */
    fun getTestMovies(playlistId: Long): List<Movie> {
        return listOf(
            // Big Buck Bunny (Blender open movie)
            Movie(
                id = 300,
                playlistId = playlistId,
                name = "Big Buck Bunny",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                posterUrl = "https://peach.blender.org/wp-content/uploads/bbb-splash.png",
                backdropUrl = "https://peach.blender.org/wp-content/uploads/title_anouncement.jpg",
                description = "Big Buck Bunny tells the story of a giant rabbit with a heart bigger than himself. When one sunny day three rodents rudely harass his two favorite butterflies, something snaps... and the rabbit's nasty streak is revealed.",
                genre = "Animation, Comedy, Family",
                year = 2008,
                rating = 7.2f,
                duration = 10,
                director = "Sacha Goedegebure",
                cast = "Frank Griebe, Sacha Goedegebure"
            ),
            
            // Elephant Dream (Blender open movie)
            Movie(
                id = 301,
                playlistId = playlistId,
                name = "Elephants Dream",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                posterUrl = "https://orange.blender.org/wp-content/themes/orange/images/media/gallery/s1_proog.jpg",
                description = "The first open movie from Blender Foundation, made entirely with free software.",
                genre = "Animation, Sci-Fi",
                year = 2006,
                rating = 6.8f,
                duration = 11,
                director = "Bassam Kurdali",
                cast = "Tygo Gernandt, Cas Jansen"
            ),
            
            // Sintel (Blender open movie)
            Movie(
                id = 302,
                playlistId = playlistId,
                name = "Sintel",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4",
                posterUrl = "https://durian.blender.org/wp-content/uploads/2010/06/sintel_trailer_1080p.png",
                description = "A lonely young woman, Sintel, helps and befriends a dragon, whom she calls Scales. But when he is kidnapped by an adult dragon, Sintel decides to embark on a dangerous quest to find her lost friend Scales.",
                genre = "Animation, Adventure, Drama",
                year = 2010,
                rating = 7.5f,
                duration = 15,
                director = "Colin Levy",
                cast = "Halina Reijn, Thom Hoffman"
            ),
            
            // Tears of Steel (Blender open movie)
            Movie(
                id = 303,
                playlistId = playlistId,
                name = "Tears of Steel",
                streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                posterUrl = "https://mango.blender.org/wp-content/uploads/2012/09/01_thom_celia_bridge.jpg",
                description = "Tears of Steel was realized with crowd-funding by users of the open source 3D creation tool Blender. Target was to improve and test a complete open and free pipeline for visual effects in film.",
                genre = "Sci-Fi, Action",
                year = 2012,
                rating = 7.1f,
                duration = 12,
                director = "Ian Hubert",
                cast = "Derek de Lint, Berenice Bejo"
            )
        )
    }
    
    /**
     * Get test series with episodes
     */
    fun getTestSeries(playlistId: Long): List<Series> {
        return listOf(
            Series(
                id = 400,
                playlistId = playlistId,
                name = "Blender Open Movies Collection",
                posterUrl = "https://www.blender.org/wp-content/uploads/2019/07/blender_logo_socket.png",
                description = "A collection of open source movies created with Blender.",
                genre = "Animation, Various",
                year = 2006,
                rating = 8.0f,
                totalSeasons = 1,
                totalEpisodes = 4,
                director = "Blender Foundation",
                cast = "Various Artists"
            )
        )
    }
    
    /**
     * Get test episodes for series
     */
    fun getTestEpisodes(seriesId: Long): List<Episode> {
        return when (seriesId) {
            400L -> listOf(
                Episode(
                    id = 500,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 1,
                    name = "Elephants Dream",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                    description = "The first open movie from Blender Foundation.",
                    duration = 11,
                    airDate = "2006-03-24"
                ),
                Episode(
                    id = 501,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 2,
                    name = "Big Buck Bunny",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                    description = "A story about a giant rabbit with a big heart.",
                    duration = 10,
                    airDate = "2008-04-10"
                ),
                Episode(
                    id = 502,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 3,
                    name = "Sintel",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4",
                    description = "A young woman's quest to find her dragon friend.",
                    duration = 15,
                    airDate = "2010-09-27"
                ),
                Episode(
                    id = 503,
                    seriesId = seriesId,
                    seasonNumber = 1,
                    episodeNumber = 4,
                    name = "Tears of Steel",
                    streamUrl = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4",
                    description = "A sci-fi short film with visual effects.",
                    duration = 12,
                    airDate = "2012-09-26"
                )
            )
            else -> emptyList()
        }
    }
    
    /**
     * Initialize test data in database
     */
    suspend fun initializeTestData(repository: com.streamr.iptv.data.repository.StreamrRepository) {
        try {
            // Add test playlists
            val playlists = getTestPlaylists()
            playlists.forEach { playlist ->
                val playlistId = repository.insertPlaylist(playlist)
                
                // Add test channels
                val channels = getTestChannels(playlistId)
                if (channels.isNotEmpty()) {
                    repository.insertChannels(channels)
                }
                
                // Add test movies
                val movies = getTestMovies(playlistId)
                if (movies.isNotEmpty()) {
                    repository.insertMovies(movies)
                }
                
                // Add test series
                val series = getTestSeries(playlistId)
                if (series.isNotEmpty()) {
                    repository.insertSeries(series)
                    
                    // Add test episodes
                    series.forEach { seriesItem ->
                        val episodes = getTestEpisodes(seriesItem.id)
                        if (episodes.isNotEmpty()) {
                            repository.insertEpisodes(episodes)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
