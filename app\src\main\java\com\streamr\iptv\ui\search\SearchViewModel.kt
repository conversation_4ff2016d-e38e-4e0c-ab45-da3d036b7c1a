package com.streamr.iptv.ui.search

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class SearchViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    private val _searchResults = MediatorLiveData<List<MediaItem>>()
    val searchResults: LiveData<List<MediaItem>> = _searchResults
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    fun search(query: String) {
        _isLoading.value = true
        
        // Search in channels
        val channelResults = repository.searchChannels(query)
        val movieResults = repository.searchMovies(query)
        val seriesResults = repository.searchSeries(query)
        
        _searchResults.addSource(channelResults) { channels ->
            val combined = mutableListOf<MediaItem>()
            channels?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            movieResults.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            seriesResults.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            _searchResults.value = combined
            _isLoading.value = false
        }
        
        _searchResults.addSource(movieResults) { movies ->
            val combined = mutableListOf<MediaItem>()
            channelResults.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            movies?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            seriesResults.value?.let { combined.addAll(it.map { series -> MediaItem.fromSeries(series) }) }
            _searchResults.value = combined
            _isLoading.value = false
        }
        
        _searchResults.addSource(seriesResults) { series ->
            val combined = mutableListOf<MediaItem>()
            channelResults.value?.let { combined.addAll(it.map { channel -> MediaItem.fromChannel(channel) }) }
            movieResults.value?.let { combined.addAll(it.map { movie -> MediaItem.fromMovie(movie) }) }
            series?.let { combined.addAll(it.map { s -> MediaItem.fromSeries(s) }) }
            _searchResults.value = combined
            _isLoading.value = false
        }
    }
    
    fun clearSearch() {
        _searchResults.value = emptyList()
        _isLoading.value = false
    }
    
    fun playItem(item: MediaItem, context: Context) {
        viewModelScope.launch {
            try {
                when (item.type) {
                    MediaItem.Type.CHANNEL -> {
                        // Get full channel data and play
                        val channel = repository.getChannelById(item.id)
                        channel?.let {
                            PlayerActivity.startWithChannel(context, it)
                        }
                    }
                    MediaItem.Type.MOVIE -> {
                        // Get full movie data and play
                        val movie = repository.getMovieById(item.id)
                        movie?.let {
                            PlayerActivity.startWithMovie(context, it, item.progress)
                        }
                    }
                    MediaItem.Type.SERIES -> {
                        // For series, get the first unwatched episode
                        val episodes = repository.getEpisodesBySeries(item.id).value
                        val nextEpisode = episodes?.firstOrNull { !it.isWatched } ?: episodes?.firstOrNull()
                        nextEpisode?.let {
                            PlayerActivity.startWithEpisode(context, it, it.watchProgress)
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
