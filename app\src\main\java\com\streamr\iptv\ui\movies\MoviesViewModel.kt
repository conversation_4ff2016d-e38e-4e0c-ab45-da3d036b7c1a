package com.streamr.iptv.ui.movies

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.adapters.MediaItem
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class MoviesViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: StreamrRepository = (application as StreamrApplication).repository
    
    val movies: LiveData<List<MediaItem>> = MediatorLiveData<List<MediaItem>>().apply {
        val playlists = repository.getActivePlaylists()
        addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                val firstPlaylist = playlistList.first()
                removeSource(playlists)
                addSource(repository.getMoviesByPlaylist(firstPlaylist.id)) { movieList ->
                    value = movieList?.map { MediaItem.fromMovie(it) } ?: emptyList()
                }
            } else {
                value = emptyList()
            }
        }
    }
    
    fun playMovie(movieItem: MediaItem, context: Context) {
        viewModelScope.launch {
            try {
                // Get full movie data and play
                val movie = repository.getMovieById(movieItem.id)
                movie?.let {
                    PlayerActivity.startWithMovie(context, it, movieItem.progress)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun toggleFavorite(movieItem: MediaItem) {
        viewModelScope.launch {
            try {
                repository.updateMovieFavoriteStatus(movieItem.id, !movieItem.isFavorite)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
