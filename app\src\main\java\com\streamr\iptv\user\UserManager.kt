package com.streamr.iptv.user

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.streamr.iptv.data.model.*
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Manages user profiles, authentication, and user-specific data
 */
class UserManager(
    private val context: Context,
    private val repository: StreamrRepository
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("user_prefs", Context.MODE_PRIVATE)
    private val scope = CoroutineScope(Dispatchers.IO)
    
    private val _currentUser = MutableLiveData<UserProfile?>()
    val currentUser: LiveData<UserProfile?> = _currentUser
    
    private val _allProfiles = MutableLiveData<List<UserProfile>>()
    val allProfiles: LiveData<List<UserProfile>> = _allProfiles
    
    companion object {
        private const val PREF_CURRENT_USER_ID = "current_user_id"
        private const val PREF_AUTO_LOGIN = "auto_login"
        private const val PREF_REMEMBER_LAST_USER = "remember_last_user"
    }
    
    init {
        loadCurrentUser()
        loadAllProfiles()
    }
    
    /**
     * Create a new user profile
     */
    suspend fun createProfile(
        name: String,
        email: String? = null,
        isKidsProfile: Boolean = false,
        parentalControlPin: String? = null
    ): Result<UserProfile> {
        return try {
            val profile = UserProfile(
                name = name,
                email = email,
                isKidsProfile = isKidsProfile,
                parentalControlPin = parentalControlPin,
                maxRating = if (isKidsProfile) "PG" else "R"
            )
            
            val profileId = repository.insertUserProfile(profile)
            val createdProfile = profile.copy(id = profileId)
            
            // Create default settings for the user
            createDefaultSettings(profileId)
            
            // Refresh profiles list
            loadAllProfiles()
            
            Result.success(createdProfile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Switch to a different user profile
     */
    suspend fun switchToProfile(profileId: Long, pin: String? = null): Result<UserProfile> {
        return try {
            val profile = repository.getUserProfileById(profileId)
                ?: return Result.failure(Exception("Profile not found"))
            
            // Check parental control PIN if required
            if (!profile.parentalControlPin.isNullOrEmpty() && profile.parentalControlPin != pin) {
                return Result.failure(Exception("Invalid PIN"))
            }
            
            // Update last login time
            repository.updateUserLastLogin(profileId, System.currentTimeMillis())
            
            // Save current user preference
            prefs.edit().putLong(PREF_CURRENT_USER_ID, profileId).apply()
            
            // Update current user
            _currentUser.postValue(profile)
            
            Result.success(profile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Update user profile
     */
    suspend fun updateProfile(profile: UserProfile): Result<UserProfile> {
        return try {
            val updatedProfile = profile.copy(updatedAt = System.currentTimeMillis())
            repository.updateUserProfile(updatedProfile)
            
            // Update current user if it's the same profile
            if (_currentUser.value?.id == profile.id) {
                _currentUser.postValue(updatedProfile)
            }
            
            // Refresh profiles list
            loadAllProfiles()
            
            Result.success(updatedProfile)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Delete user profile
     */
    suspend fun deleteProfile(profileId: Long): Result<Unit> {
        return try {
            // Don't allow deleting the last profile
            val allProfiles = repository.getAllUserProfiles()
            if (allProfiles.size <= 1) {
                return Result.failure(Exception("Cannot delete the last profile"))
            }
            
            repository.deleteUserProfile(profileId)
            
            // If deleted profile was current user, switch to another profile
            if (_currentUser.value?.id == profileId) {
                val remainingProfiles = allProfiles.filter { it.id != profileId }
                if (remainingProfiles.isNotEmpty()) {
                    switchToProfile(remainingProfiles.first().id)
                }
            }
            
            // Refresh profiles list
            loadAllProfiles()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Add item to watch history
     */
    suspend fun addToWatchHistory(
        mediaType: WatchMediaType,
        mediaId: Long,
        mediaTitle: String,
        watchedDuration: Long,
        totalDuration: Long,
        lastPosition: Long,
        mediaThumbnail: String? = null
    ) {
        val currentUserId = _currentUser.value?.id ?: return
        
        try {
            val watchProgress = if (totalDuration > 0) {
                (watchedDuration.toFloat() / totalDuration.toFloat()).coerceIn(0f, 1f)
            } else {
                0f
            }
            
            val historyItem = WatchHistory(
                userId = currentUserId,
                mediaType = mediaType,
                mediaId = mediaId,
                mediaTitle = mediaTitle,
                mediaThumbnail = mediaThumbnail,
                watchedDuration = watchedDuration,
                totalDuration = totalDuration,
                watchProgress = watchProgress,
                lastPosition = lastPosition,
                isCompleted = watchProgress >= 0.9f
            )
            
            repository.insertWatchHistory(historyItem)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * Add item to favorites
     */
    suspend fun addToFavorites(
        mediaType: FavoriteMediaType,
        mediaId: Long,
        mediaTitle: String,
        mediaThumbnail: String? = null
    ): Result<Unit> {
        val currentUserId = _currentUser.value?.id ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            val favorite = UserFavorite(
                userId = currentUserId,
                mediaType = mediaType,
                mediaId = mediaId,
                mediaTitle = mediaTitle,
                mediaThumbnail = mediaThumbnail
            )
            
            repository.insertUserFavorite(favorite)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Remove item from favorites
     */
    suspend fun removeFromFavorites(mediaType: FavoriteMediaType, mediaId: Long): Result<Unit> {
        val currentUserId = _currentUser.value?.id ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            repository.removeUserFavorite(currentUserId, mediaType, mediaId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Add item to watchlist
     */
    suspend fun addToWatchlist(
        mediaType: WatchlistMediaType,
        mediaId: Long,
        mediaTitle: String,
        mediaThumbnail: String? = null,
        mediaDescription: String? = null
    ): Result<Unit> {
        val currentUserId = _currentUser.value?.id ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            val watchlistItem = UserWatchlist(
                userId = currentUserId,
                mediaType = mediaType,
                mediaId = mediaId,
                mediaTitle = mediaTitle,
                mediaThumbnail = mediaThumbnail,
                mediaDescription = mediaDescription
            )
            
            repository.insertUserWatchlist(watchlistItem)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Remove item from watchlist
     */
    suspend fun removeFromWatchlist(mediaType: WatchlistMediaType, mediaId: Long): Result<Unit> {
        val currentUserId = _currentUser.value?.id ?: return Result.failure(Exception("No user logged in"))
        
        return try {
            repository.removeUserWatchlist(currentUserId, mediaType, mediaId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Get user's watch history
     */
    fun getWatchHistory(): LiveData<List<WatchHistory>> {
        val currentUserId = _currentUser.value?.id ?: return MutableLiveData(emptyList())
        return repository.getUserWatchHistory(currentUserId)
    }
    
    /**
     * Get user's favorites
     */
    fun getFavorites(): LiveData<List<UserFavorite>> {
        val currentUserId = _currentUser.value?.id ?: return MutableLiveData(emptyList())
        return repository.getUserFavorites(currentUserId)
    }
    
    /**
     * Get user's watchlist
     */
    fun getWatchlist(): LiveData<List<UserWatchlist>> {
        val currentUserId = _currentUser.value?.id ?: return MutableLiveData(emptyList())
        return repository.getUserWatchlist(currentUserId)
    }
    
    /**
     * Get user settings
     */
    suspend fun getUserSettings(): UserSettings? {
        val currentUserId = _currentUser.value?.id ?: return null
        return repository.getUserSettings(currentUserId)
    }
    
    /**
     * Update user settings
     */
    suspend fun updateUserSettings(settings: UserSettings): Result<Unit> {
        return try {
            val updatedSettings = settings.copy(updatedAt = System.currentTimeMillis())
            repository.updateUserSettings(updatedSettings)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Check if media is in favorites
     */
    suspend fun isInFavorites(mediaType: FavoriteMediaType, mediaId: Long): Boolean {
        val currentUserId = _currentUser.value?.id ?: return false
        return repository.isInUserFavorites(currentUserId, mediaType, mediaId)
    }
    
    /**
     * Check if media is in watchlist
     */
    suspend fun isInWatchlist(mediaType: WatchlistMediaType, mediaId: Long): Boolean {
        val currentUserId = _currentUser.value?.id ?: return false
        return repository.isInUserWatchlist(currentUserId, mediaType, mediaId)
    }
    
    /**
     * Load current user from preferences
     */
    private fun loadCurrentUser() {
        scope.launch {
            try {
                val currentUserId = prefs.getLong(PREF_CURRENT_USER_ID, -1L)
                if (currentUserId != -1L) {
                    val profile = repository.getUserProfileById(currentUserId)
                    _currentUser.postValue(profile)
                } else {
                    // No current user, try to get the first available profile
                    val profiles = repository.getAllUserProfiles()
                    if (profiles.isNotEmpty()) {
                        switchToProfile(profiles.first().id)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    /**
     * Load all user profiles
     */
    private fun loadAllProfiles() {
        scope.launch {
            try {
                val profiles = repository.getAllUserProfiles()
                _allProfiles.postValue(profiles)
            } catch (e: Exception) {
                e.printStackTrace()
                _allProfiles.postValue(emptyList())
            }
        }
    }
    
    /**
     * Create default settings for a new user
     */
    private suspend fun createDefaultSettings(userId: Long) {
        try {
            val defaultSettings = UserSettings(userId = userId)
            repository.insertUserSettings(defaultSettings)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
