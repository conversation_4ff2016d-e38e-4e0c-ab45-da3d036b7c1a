package com.streamr.iptv.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import androidx.room.Index
import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * Download task entity
 */
@Entity(
    tableName = "downloads",
    indices = [
        Index(value = ["status"]),
        Index(value = ["mediaType"]),
        Index(value = ["createdAt"])
    ]
)
@Parcelize
data class Download(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // Media information
    val mediaId: Long,
    val mediaType: DownloadMediaType,
    val title: String,
    val description: String? = null,
    val thumbnailUrl: String? = null,
    val streamUrl: String,
    
    // Download settings
    val quality: DownloadQuality = DownloadQuality.HD_720P,
    val audioLanguage: String? = null,
    val subtitleLanguage: String? = null,
    
    // File information
    val fileName: String,
    val filePath: String? = null,
    val fileSize: Long = 0, // Total file size in bytes
    val downloadedSize: Long = 0, // Downloaded size in bytes
    
    // Download status
    val status: DownloadStatus = DownloadStatus.PENDING,
    val progress: Int = 0, // Progress percentage (0-100)
    val speed: Long = 0, // Download speed in bytes/second
    val error: String? = null,
    
    // Timestamps
    val createdAt: Long = System.currentTimeMillis(),
    val startedAt: Long? = null,
    val completedAt: Long? = null,
    val pausedAt: Long? = null,
    
    // Additional metadata
    val duration: Int? = null, // Duration in minutes
    val episodeNumber: Int? = null,
    val seasonNumber: Int? = null,
    val isAutoDownload: Boolean = false, // For series auto-download
    val expiresAt: Long? = null // When the download expires (for temporary downloads)
) : Parcelable {
    
    /**
     * Get progress percentage as float (0.0 - 1.0)
     */
    fun getProgressFloat(): Float = progress / 100f
    
    /**
     * Get remaining download size in bytes
     */
    fun getRemainingSize(): Long = fileSize - downloadedSize
    
    /**
     * Get estimated time remaining in seconds
     */
    fun getEstimatedTimeRemaining(): Long {
        if (speed <= 0 || status != DownloadStatus.DOWNLOADING) return -1
        return getRemainingSize() / speed
    }
    
    /**
     * Check if download is active (downloading or paused)
     */
    fun isActive(): Boolean = status in listOf(
        DownloadStatus.DOWNLOADING,
        DownloadStatus.PAUSED,
        DownloadStatus.PENDING,
        DownloadStatus.QUEUED
    )
    
    /**
     * Check if download is completed successfully
     */
    fun isCompleted(): Boolean = status == DownloadStatus.COMPLETED
    
    /**
     * Check if download has failed
     */
    fun isFailed(): Boolean = status == DownloadStatus.FAILED
    
    /**
     * Get formatted file size
     */
    fun getFormattedFileSize(): String = formatBytes(fileSize)
    
    /**
     * Get formatted downloaded size
     */
    fun getFormattedDownloadedSize(): String = formatBytes(downloadedSize)
    
    /**
     * Get formatted download speed
     */
    fun getFormattedSpeed(): String = "${formatBytes(speed)}/s"
    
    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1_073_741_824 -> String.format("%.1f GB", bytes / 1_073_741_824.0)
            bytes >= 1_048_576 -> String.format("%.1f MB", bytes / 1_048_576.0)
            bytes >= 1024 -> String.format("%.1f KB", bytes / 1024.0)
            else -> "$bytes B"
        }
    }
}

/**
 * Download media types
 */
enum class DownloadMediaType {
    MOVIE,
    EPISODE,
    LIVE_RECORDING
}

/**
 * Download quality options
 */
enum class DownloadQuality(val displayName: String, val maxHeight: Int, val estimatedBitrate: Int) {
    SD_480P("480p", 480, 1_500_000),
    HD_720P("720p", 720, 3_000_000),
    FHD_1080P("1080p", 1080, 6_000_000),
    UHD_4K("4K", 2160, 15_000_000)
}

/**
 * Download status
 */
enum class DownloadStatus {
    PENDING,        // Waiting to start
    QUEUED,         // In download queue
    DOWNLOADING,    // Currently downloading
    PAUSED,         // Paused by user
    COMPLETED,      // Download completed successfully
    FAILED,         // Download failed
    CANCELLED,      // Cancelled by user
    EXPIRED         // Download expired and removed
}

/**
 * Recording task entity for live TV recording
 */
@Entity(
    tableName = "recordings",
    foreignKeys = [
        ForeignKey(
            entity = Channel::class,
            parentColumns = ["id"],
            childColumns = ["channelId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = EPGProgram::class,
            parentColumns = ["id"],
            childColumns = ["programId"],
            onDelete = ForeignKey.SET_NULL
        )
    ],
    indices = [
        Index(value = ["channelId"]),
        Index(value = ["programId"]),
        Index(value = ["status"]),
        Index(value = ["scheduledStartTime"])
    ]
)
@Parcelize
data class Recording(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    // Channel and program information
    val channelId: Long,
    val programId: Long? = null, // Null for manual recordings
    val channelName: String,
    val programTitle: String? = null,
    val programDescription: String? = null,
    
    // Recording schedule
    val scheduledStartTime: Long,
    val scheduledEndTime: Long,
    val actualStartTime: Long? = null,
    val actualEndTime: Long? = null,
    
    // Recording settings
    val quality: DownloadQuality = DownloadQuality.HD_720P,
    val isSeriesRecording: Boolean = false, // Record all episodes of a series
    val recordingType: RecordingType = RecordingType.SINGLE,
    
    // File information
    val fileName: String,
    val filePath: String? = null,
    val fileSize: Long = 0,
    
    // Recording status
    val status: RecordingStatus = RecordingStatus.SCHEDULED,
    val progress: Int = 0,
    val error: String? = null,
    
    // Timestamps
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * Get scheduled duration in minutes
     */
    fun getScheduledDuration(): Int {
        return ((scheduledEndTime - scheduledStartTime) / (60 * 1000)).toInt()
    }
    
    /**
     * Get actual duration in minutes
     */
    fun getActualDuration(): Int? {
        return if (actualStartTime != null && actualEndTime != null) {
            ((actualEndTime - actualStartTime) / (60 * 1000)).toInt()
        } else null
    }
    
    /**
     * Check if recording is currently active
     */
    fun isActive(): Boolean = status == RecordingStatus.IN_PROGRESS
    
    /**
     * Check if recording is scheduled for future
     */
    fun isScheduled(): Boolean = status == RecordingStatus.SCHEDULED
    
    /**
     * Check if recording is completed
     */
    fun isCompleted(): Boolean = status == RecordingStatus.COMPLETED
    
    /**
     * Get time until recording starts (in milliseconds)
     */
    fun getTimeUntilStart(): Long {
        val now = System.currentTimeMillis()
        return if (scheduledStartTime > now) scheduledStartTime - now else 0
    }
    
    /**
     * Get formatted file size
     */
    fun getFormattedFileSize(): String {
        return when {
            fileSize >= 1_073_741_824 -> String.format("%.1f GB", fileSize / 1_073_741_824.0)
            fileSize >= 1_048_576 -> String.format("%.1f MB", fileSize / 1_048_576.0)
            fileSize >= 1024 -> String.format("%.1f KB", fileSize / 1024.0)
            else -> "$fileSize B"
        }
    }
}

/**
 * Recording types
 */
enum class RecordingType {
    SINGLE,         // Record single program
    SERIES,         // Record all episodes of a series
    TIME_BASED      // Record based on time slot
}

/**
 * Download queue item for managing download order
 */
@Entity(
    tableName = "download_queue",
    foreignKeys = [
        ForeignKey(
            entity = Download::class,
            parentColumns = ["id"],
            childColumns = ["downloadId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["downloadId"]), Index(value = ["priority"])]
)
@Parcelize
data class DownloadQueueItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    val downloadId: Long,
    val priority: Int = 0, // Higher number = higher priority
    val addedAt: Long = System.currentTimeMillis()
) : Parcelable

/**
 * Download settings for the app
 */
data class DownloadSettings(
    val maxConcurrentDownloads: Int = 2,
    val defaultQuality: DownloadQuality = DownloadQuality.HD_720P,
    val downloadOnlyOnWifi: Boolean = true,
    val downloadOnlyWhenCharging: Boolean = false,
    val autoDeleteAfterDays: Int = 30,
    val maxStorageUsageGB: Int = 10,
    val downloadLocation: String = "Downloads/StreamrIPTV"
)
