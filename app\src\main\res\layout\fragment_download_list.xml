<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark">

    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:padding="8dp" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/emptyState"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/ic_download"
            app:tint="@color/text_secondary" />

        <TextView
            android:id="@+id/tvEmptyMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No downloads"
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Downloads will appear here"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

</FrameLayout>
