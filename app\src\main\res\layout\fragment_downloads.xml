<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_dark"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Downloads"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Settings button -->
        <ImageButton
            android:id="@+id/btnDownloadSettings"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_settings"
            app:tint="@color/text_primary" />

    </LinearLayout>

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_medium"
        app:tabIndicatorColor="@color/accent_green"
        app:tabSelectedTextColor="@color/accent_green"
        app:tabTextColor="@color/text_secondary" />

    <!-- ViewPager for tabs -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Download controls -->
    <LinearLayout
        android:id="@+id/downloadControls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_dark"
        android:elevation="4dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="12dp"
        android:visibility="gone">

        <!-- Pause All -->
        <Button
            android:id="@+id/btnPauseAll"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Pause All"
            android:textColor="@color/text_primary" />

        <!-- Resume All -->
        <Button
            android:id="@+id/btnResumeAll"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="Resume All"
            android:textColor="@color/accent_green" />

        <!-- Clear Completed -->
        <Button
            android:id="@+id/btnClearCompleted"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Clear Completed"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

</LinearLayout>
