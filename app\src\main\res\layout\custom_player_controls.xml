<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Spacer to center the controls -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Center Play/Pause Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Rewind Button -->
        <ImageButton
            android:id="@id/exo_rew"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/button_background"
            android:src="@drawable/ic_rewind"
            app:tint="@color/white" />

        <!-- Play/Pause Button -->
        <ImageButton
            android:id="@id/exo_play_pause"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/button_background"
            android:src="@drawable/ic_play"
            app:tint="@color/white" />

        <!-- Fast Forward Button -->
        <ImageButton
            android:id="@id/exo_ffwd"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="24dp"
            android:background="@drawable/button_background"
            android:src="@drawable/ic_fast_forward"
            app:tint="@color/white" />

    </LinearLayout>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Bottom Progress and Time Controls -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/gradient_bottom_overlay"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Progress Bar -->
        <com.google.android.exoplayer2.ui.DefaultTimeBar
            android:id="@id/exo_progress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:bar_height="4dp"
            app:buffered_color="@color/accent_light_green"
            app:played_color="@color/accent_green"
            app:scrubber_color="@color/accent_green"
            app:unplayed_color="@color/text_secondary" />

        <!-- Time Display -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Current Time -->
            <TextView
                android:id="@id/exo_position"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="00:00"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <!-- Separator -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="/"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- Total Duration -->
            <TextView
                android:id="@id/exo_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:includeFontPadding="false"
                android:text="00:00"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

            <!-- Settings Button (for quality, subtitles, etc.) -->
            <ImageButton
                android:id="@+id/btnPlayerSettings"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_settings"
                app:tint="@color/white" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
