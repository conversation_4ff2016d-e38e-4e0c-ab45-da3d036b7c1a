package com.streamr.iptv.ui.livetv

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.streamr.iptv.StreamrApplication
import com.streamr.iptv.data.model.Channel
import com.streamr.iptv.data.model.ChannelCategory
import com.streamr.iptv.data.repository.StreamrRepository
import com.streamr.iptv.ui.player.PlayerActivity
import kotlinx.coroutines.launch

class LiveTVViewModel(application: Application) : AndroidViewModel(application) {

    private val repository: StreamrRepository = (application as StreamrApplication).repository

    private val _channels = MediatorLiveData<List<Channel>>()
    val channels: LiveData<List<Channel>> = _channels

    private val _categorySelected = MutableLiveData<Boolean>()
    val categorySelected: LiveData<Boolean> = _categorySelected

    private var currentPlaylistId: Long? = null

    init {
        loadAllChannels()
    }

    private fun loadAllChannels() {
        val playlists = repository.getActivePlaylists()
        _channels.addSource(playlists) { playlistList ->
            if (playlistList.isNotEmpty()) {
                val firstPlaylist = playlistList.first()
                currentPlaylistId = firstPlaylist.id
                _channels.removeSource(playlists)
                _channels.addSource(repository.getChannelsByPlaylist(firstPlaylist.id)) { channelList ->
                    _channels.value = channelList ?: emptyList()
                }
            } else {
                _channels.value = emptyList()
            }
        }
    }

    fun loadChannelsByCategory(category: ChannelCategory) {
        currentPlaylistId?.let { playlistId ->
            _channels.removeSource(repository.getChannelsByPlaylist(playlistId))
            _channels.addSource(repository.getChannelsByCategory(playlistId, category)) { channelList ->
                _channels.value = channelList ?: emptyList()
            }
            _categorySelected.value = true
        }
    }

    fun playChannel(channel: Channel, context: Context) {
        viewModelScope.launch {
            try {
                // Update watch history
                repository.updateChannelWatchHistory(channel.id)

                // Start PlayerActivity with the channel
                PlayerActivity.startWithChannel(context, channel)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun toggleChannelFavorite(channel: Channel) {
        viewModelScope.launch {
            try {
                repository.updateChannelFavoriteStatus(channel.id, !channel.isFavorite)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
