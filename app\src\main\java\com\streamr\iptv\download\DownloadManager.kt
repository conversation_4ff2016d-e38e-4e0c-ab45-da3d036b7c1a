package com.streamr.iptv.download

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.BatteryManager
import android.os.Environment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.streamr.iptv.data.model.*
import com.streamr.iptv.data.repository.StreamrRepository
import kotlinx.coroutines.*
import okhttp3.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 * Manages downloads and recordings for the app
 */
class DownloadManager(
    private val context: Context,
    private val repository: StreamrRepository
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val activeDownloads = ConcurrentHashMap<Long, DownloadTask>()
    private val downloadSettings = MutableLiveData<DownloadSettings>()
    
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    init {
        loadDownloadSettings()
        startDownloadQueue()
    }
    
    /**
     * Start a new download
     */
    suspend fun startDownload(
        mediaId: Long,
        mediaType: DownloadMediaType,
        title: String,
        streamUrl: String,
        quality: DownloadQuality = DownloadQuality.HD_720P,
        description: String? = null,
        thumbnailUrl: String? = null
    ): Result<Long> {
        return try {
            // Check if download conditions are met
            if (!canStartDownload()) {
                return Result.failure(Exception("Download conditions not met"))
            }
            
            // Create download entry
            val fileName = generateFileName(title, quality)
            val download = Download(
                mediaId = mediaId,
                mediaType = mediaType,
                title = title,
                description = description,
                thumbnailUrl = thumbnailUrl,
                streamUrl = streamUrl,
                quality = quality,
                fileName = fileName,
                status = DownloadStatus.PENDING
            )
            
            val downloadId = repository.insertDownload(download)
            
            // Add to queue
            repository.addToDownloadQueue(downloadId)
            
            Result.success(downloadId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Pause a download
     */
    suspend fun pauseDownload(downloadId: Long) {
        activeDownloads[downloadId]?.pause()
        repository.updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
    }
    
    /**
     * Resume a download
     */
    suspend fun resumeDownload(downloadId: Long) {
        val download = repository.getDownloadById(downloadId)
        if (download?.status == DownloadStatus.PAUSED) {
            repository.updateDownloadStatus(downloadId, DownloadStatus.QUEUED)
        }
    }
    
    /**
     * Cancel a download
     */
    suspend fun cancelDownload(downloadId: Long) {
        activeDownloads[downloadId]?.cancel()
        activeDownloads.remove(downloadId)
        
        val download = repository.getDownloadById(downloadId)
        download?.filePath?.let { path ->
            try {
                File(path).delete()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        repository.updateDownloadStatus(downloadId, DownloadStatus.CANCELLED)
    }
    
    /**
     * Delete a completed download
     */
    suspend fun deleteDownload(downloadId: Long) {
        val download = repository.getDownloadById(downloadId)
        download?.filePath?.let { path ->
            try {
                File(path).delete()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        
        repository.deleteDownload(downloadId)
    }
    
    /**
     * Schedule a recording
     */
    suspend fun scheduleRecording(
        channelId: Long,
        programId: Long?,
        startTime: Long,
        endTime: Long,
        quality: DownloadQuality = DownloadQuality.HD_720P
    ): Result<Long> {
        return try {
            val channel = repository.getChannelById(channelId)
                ?: return Result.failure(Exception("Channel not found"))
            
            val program = programId?.let { repository.getEPGProgramById(it) }
            
            val fileName = generateRecordingFileName(
                channel.name,
                program?.title ?: "Recording",
                startTime
            )
            
            val recording = Recording(
                channelId = channelId,
                programId = programId,
                channelName = channel.name,
                programTitle = program?.title,
                programDescription = program?.description,
                scheduledStartTime = startTime,
                scheduledEndTime = endTime,
                quality = quality,
                fileName = fileName,
                status = RecordingStatus.SCHEDULED
            )
            
            val recordingId = repository.insertRecording(recording)
            
            // Schedule the recording
            scheduleRecordingTask(recordingId, startTime)
            
            Result.success(recordingId)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * Cancel a scheduled recording
     */
    suspend fun cancelRecording(recordingId: Long) {
        repository.updateRecordingStatus(recordingId, RecordingStatus.CANCELLED)
        // Cancel any scheduled tasks for this recording
    }
    
    /**
     * Get all downloads
     */
    fun getAllDownloads(): LiveData<List<Download>> {
        return repository.getAllDownloads()
    }
    
    /**
     * Get downloads by status
     */
    fun getDownloadsByStatus(status: DownloadStatus): LiveData<List<Download>> {
        return repository.getDownloadsByStatus(status)
    }
    
    /**
     * Get all recordings
     */
    fun getAllRecordings(): LiveData<List<Recording>> {
        return repository.getAllRecordings()
    }
    
    /**
     * Check if download conditions are met
     */
    private fun canStartDownload(): Boolean {
        val settings = downloadSettings.value ?: DownloadSettings()
        
        // Check WiFi requirement
        if (settings.downloadOnlyOnWifi && !isWifiConnected()) {
            return false
        }
        
        // Check charging requirement
        if (settings.downloadOnlyWhenCharging && !isCharging()) {
            return false
        }
        
        // Check storage space
        if (!hasEnoughStorage()) {
            return false
        }
        
        // Check concurrent downloads limit
        if (activeDownloads.size >= settings.maxConcurrentDownloads) {
            return false
        }
        
        return true
    }
    
    /**
     * Check if device is connected to WiFi
     */
    private fun isWifiConnected(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork
        val capabilities = connectivityManager.getNetworkCapabilities(network)
        return capabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true
    }
    
    /**
     * Check if device is charging
     */
    private fun isCharging(): Boolean {
        val batteryManager = context.getSystemService(Context.BATTERY_SERVICE) as BatteryManager
        return batteryManager.isCharging
    }
    
    /**
     * Check if there's enough storage space
     */
    private fun hasEnoughStorage(): Boolean {
        val downloadDir = getDownloadDirectory()
        val freeSpace = downloadDir.freeSpace
        val requiredSpace = 1024 * 1024 * 1024L // 1GB minimum
        return freeSpace > requiredSpace
    }
    
    /**
     * Get download directory
     */
    private fun getDownloadDirectory(): File {
        val settings = downloadSettings.value ?: DownloadSettings()
        val externalDir = context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
        val downloadDir = File(externalDir, settings.downloadLocation)
        
        if (!downloadDir.exists()) {
            downloadDir.mkdirs()
        }
        
        return downloadDir
    }
    
    /**
     * Generate file name for download
     */
    private fun generateFileName(title: String, quality: DownloadQuality): String {
        val cleanTitle = title.replace(Regex("[^a-zA-Z0-9\\s]"), "").trim()
        val timestamp = System.currentTimeMillis()
        return "${cleanTitle}_${quality.displayName}_$timestamp.mp4"
    }
    
    /**
     * Generate file name for recording
     */
    private fun generateRecordingFileName(channelName: String, programTitle: String, startTime: Long): String {
        val cleanChannel = channelName.replace(Regex("[^a-zA-Z0-9\\s]"), "").trim()
        val cleanProgram = programTitle.replace(Regex("[^a-zA-Z0-9\\s]"), "").trim()
        val dateFormat = java.text.SimpleDateFormat("yyyyMMdd_HHmm", java.util.Locale.getDefault())
        val timeString = dateFormat.format(java.util.Date(startTime))
        return "${cleanChannel}_${cleanProgram}_$timeString.mp4"
    }
    
    /**
     * Start download queue processor
     */
    private fun startDownloadQueue() {
        scope.launch {
            while (true) {
                try {
                    processDownloadQueue()
                    delay(5000) // Check every 5 seconds
                } catch (e: Exception) {
                    e.printStackTrace()
                    delay(10000) // Wait longer on error
                }
            }
        }
    }
    
    /**
     * Process download queue
     */
    private suspend fun processDownloadQueue() {
        if (!canStartDownload()) return
        
        val queuedDownloads = repository.getQueuedDownloads()
        val settings = downloadSettings.value ?: DownloadSettings()
        
        queuedDownloads.take(settings.maxConcurrentDownloads - activeDownloads.size).forEach { download ->
            startDownloadTask(download)
        }
    }
    
    /**
     * Start actual download task
     */
    private fun startDownloadTask(download: Download) {
        val task = DownloadTask(download, repository, okHttpClient, getDownloadDirectory())
        activeDownloads[download.id] = task
        
        scope.launch {
            try {
                task.start()
            } finally {
                activeDownloads.remove(download.id)
            }
        }
    }
    
    /**
     * Schedule recording task
     */
    private fun scheduleRecordingTask(recordingId: Long, startTime: Long) {
        scope.launch {
            val delay = startTime - System.currentTimeMillis()
            if (delay > 0) {
                delay(delay)
            }
            
            // Start recording
            startRecording(recordingId)
        }
    }
    
    /**
     * Start recording
     */
    private suspend fun startRecording(recordingId: Long) {
        try {
            val recording = repository.getRecordingById(recordingId)
            if (recording?.status != RecordingStatus.SCHEDULED) return
            
            repository.updateRecordingStatus(recordingId, RecordingStatus.IN_PROGRESS)
            
            // TODO: Implement actual recording logic
            // This would involve capturing the live stream and saving it to file
            
        } catch (e: Exception) {
            repository.updateRecordingStatus(recordingId, RecordingStatus.FAILED)
        }
    }
    
    /**
     * Load download settings
     */
    private fun loadDownloadSettings() {
        // TODO: Load from SharedPreferences or database
        downloadSettings.value = DownloadSettings()
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        scope.cancel()
        activeDownloads.values.forEach { it.cancel() }
        activeDownloads.clear()
    }
}
