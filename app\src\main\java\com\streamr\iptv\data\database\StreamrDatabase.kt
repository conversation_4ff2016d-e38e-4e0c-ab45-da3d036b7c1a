package com.streamr.iptv.data.database

import android.content.Context
import androidx.room.*
import com.streamr.iptv.data.model.*

@Database(
    entities = [
        Playlist::class,
        Channel::class,
        Movie::class,
        Series::class,
        Episode::class,
        EPGProgram::class,
        EPGReminder::class,
        EPGRecording::class,
        EPGCategory::class,
        UserProfile::class,
        UserSettings::class,
        WatchHistory::class,
        UserFavorite::class,
        UserWatchlist::class
    ],
    version = 3,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class StreamrDatabase : RoomDatabase() {
    
    abstract fun playlistDao(): PlaylistDao
    abstract fun channelDao(): ChannelDao
    abstract fun movieDao(): MovieDao
    abstract fun seriesDao(): SeriesDao
    abstract fun episodeDao(): EpisodeDao
    abstract fun userProfileDao(): UserProfileDao
    abstract fun userSettingsDao(): UserSettingsDao
    abstract fun watchHistoryDao(): WatchHistoryDao
    abstract fun userFavoriteDao(): UserFavoriteDao
    abstract fun userWatchlistDao(): UserWatchlistDao
    
    companion object {
        @Volatile
        private var INSTANCE: StreamrDatabase? = null
        
        fun getDatabase(context: Context): StreamrDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    StreamrDatabase::class.java,
                    "streamr_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

class Converters {
    @TypeConverter
    fun fromPlaylistType(type: PlaylistType): String {
        return type.name
    }
    
    @TypeConverter
    fun toPlaylistType(type: String): PlaylistType {
        return PlaylistType.valueOf(type)
    }
    
    @TypeConverter
    fun fromChannelCategory(category: ChannelCategory): String {
        return category.name
    }
    
    @TypeConverter
    fun toChannelCategory(category: String): ChannelCategory {
        return ChannelCategory.valueOf(category)
    }
}
