<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp">

    <!-- App Logo -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_tv"
        app:tint="@color/accent_green" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Who's watching?"
        android:textColor="@color/text_primary"
        android:textSize="28sp"
        android:textStyle="bold" />

    <!-- Subtitle -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="48dp"
        android:text="Select your profile to continue"
        android:textColor="@color/text_secondary"
        android:textSize="16sp" />

    <!-- User Profiles Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvUserProfiles"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp" />

    <!-- Add Profile Button -->
    <LinearLayout
        android:id="@+id/btnAddProfile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/profile_add_background"
            android:padding="16dp"
            android:src="@drawable/ic_add"
            app:tint="@color/text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Add Profile"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- Manage Profiles Button -->
    <Button
        android:id="@+id/btnManageProfiles"
        style="@style/Widget.Material3.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Manage Profiles"
        android:textColor="@color/text_secondary" />

</LinearLayout>
